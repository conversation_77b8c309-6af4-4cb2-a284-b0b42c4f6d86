import SwiftUI
import SwiftMath
import Foundation

// MARK: - Global Helper Functions

/// Standalone function to test containsLaTeX logic
func testContainsLaTeX(_ text: String) -> Bool {
    var textToCheck = text

    // Always remove currency patterns from LaTeX detection, regardless of mathematical context
    // Remove simple dollar amounts like $360$, $90$, $8,000$, $9,600$, etc. but NOT variables like $x$, $y$
    // Also remove escaped dollar patterns like $\$3$, $\$105$, $\$800$, $\$50$ and $\\$15$ for LaTeX detection
    let simpleDollarPattern = #"\$(?![a-zA-Z])(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(?![a-zA-Z])\$"#

    // PRIORITY: Handle the specific $\$NUMBER$ format FIRST (both single and double backslash)
    let specificEscapedDollarPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#

    let escapedDollarPattern = #"\$\\\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(/[a-zA-Z]+)?\$"#
    let additionalEscapedPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#

    // Also remove percentage patterns like $25\%$, $15\%$, $5\\%$, etc.
    let percentagePattern = #"\$(?![a-zA-Z])(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(?![a-zA-Z])\\%\$"#

    textToCheck = textToCheck.replacingOccurrences(
        of: simpleDollarPattern,
        with: "",
        options: .regularExpression
    )
    textToCheck = textToCheck.replacingOccurrences(
        of: specificEscapedDollarPattern,
        with: "",
        options: .regularExpression
    )
    textToCheck = textToCheck.replacingOccurrences(
        of: escapedDollarPattern,
        with: "",
        options: .regularExpression
    )
    textToCheck = textToCheck.replacingOccurrences(
        of: additionalEscapedPattern,
        with: "",
        options: .regularExpression
    )
    textToCheck = textToCheck.replacingOccurrences(
        of: percentagePattern,
        with: "",
        options: .regularExpression
    )

    // Remove pure currency amounts like $20, $28.50, $-35 etc.
    // BUT preserve coordinate pairs, expressions with parentheses, commas, or letters
    if !textToCheck.contains("(") && !textToCheck.contains(",") && !textToCheck.contains(")") &&
       !textToCheck.contains(where: { $0.isLetter }) {
        let regularCurrencyPattern = #"\$-?\d+(?:\.\d+)?\$"#
        textToCheck = textToCheck.replacingOccurrences(
            of: regularCurrencyPattern,
            with: "",
            options: .regularExpression
        ).trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // Special check: if after removing all currency and percentage patterns,
    // the text is empty or only contains whitespace, then it's not LaTeX
    let cleanedText = textToCheck.trimmingCharacters(in: .whitespacesAndNewlines)
    if cleanedText.isEmpty {
        print("🔧 testContainsLaTeX: Text only contains currency/percentage patterns, treating as plain text")
        return false
    }

    // Only treat as LaTeX if there are actual LaTeX expressions after removing currency
    let hasInlineMath = textToCheck.range(of: #"\$[^$]*\\[a-zA-Z]+[^$]*\$"#, options: .regularExpression) != nil
    let hasDisplayMath = textToCheck.range(of: #"\$\$[^$]+\$\$"#, options: .regularExpression) != nil

    // Check for mathematical expressions that should be rendered as LaTeX
    let hasMathExpressions =
        // Single variables: $x$, $y$, $A$ (letters only)
        textToCheck.range(of: #"\$[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        // Variables with coefficients: $3x$, $10y$ (must contain at least one letter)
        textToCheck.range(of: #"\$\d+[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        // Algebraic expressions with operators and variables: $3x + 10y$, $x - 2y$
        textToCheck.range(of: #"\$[^$]*[a-zA-Z]+[^$]*[+\-*/][^$]*\$"#, options: .regularExpression) != nil ||
        // Simple expressions with operators: $x + y$, $a - b$
        textToCheck.range(of: #"\$[a-zA-Z]+\s*[=+\-*/]\s*[a-zA-Z0-9]+\$"#, options: .regularExpression) != nil ||
        // Coordinate pairs: $(4, 3)$, $(-4, 3)$, $(x, y)$
        textToCheck.range(of: #"\$\([^$)]*,\s*[^$)]*\)\$"#, options: .regularExpression) != nil ||
        // Mathematical expressions with parentheses: $(x + 2)$, $(-3 + 4)$
        textToCheck.range(of: #"\$\([^$)]*[+\-*/][^$)]*\)\$"#, options: .regularExpression) != nil ||
        // Remove this logic - simple numbers should be converted to plain text even in mathematical contexts
        false
        // Note: Removed escaped dollar sign detection here since these should be treated as currency, not LaTeX

    // Check for superscript patterns (like cm^2, m^3, etc.)
    let hasSuperscripts = textToCheck.range(of: #"\$[^$]*\^[^$]*\$"#, options: .regularExpression) != nil ||
                         textToCheck.contains("^2") ||
                         textToCheck.contains("^3") ||
                         textToCheck.contains("^{") ||
                         textToCheck.contains("^\\")

    // Get the LaTeX command detection
    let hasLaTeXCommand = textToCheck.contains("\\frac") ||
                          textToCheck.contains("\\sqrt") ||
                          textToCheck.contains("\\times") ||
                          textToCheck.contains("\\div") ||
                          textToCheck.contains("\\alpha") ||
                          textToCheck.contains("\\beta") ||
                          textToCheck.contains("\\gamma") ||
                          textToCheck.contains("\\theta") ||
                          textToCheck.contains("\\pi") ||
                          textToCheck.contains("\\sum") ||
                          textToCheck.contains("\\int") ||
                          textToCheck.contains("\\leq") ||
                          textToCheck.contains("\\geq") ||
                          textToCheck.contains("\\neq") ||
                          textToCheck.contains("\\angle") ||
                          textToCheck.contains("\\circ") ||
                          textToCheck.contains("\\perp") ||
                          textToCheck.contains("\\parallel") ||
                          // Also check the original text for LaTeX commands
                          text.contains("\\neq") ||
                          text.contains("\\leq") ||
                          text.contains("\\geq") ||
                          text.contains("\\frac") ||
                          text.contains("\\sqrt")

    return hasInlineMath || hasDisplayMath || hasMathExpressions || hasSuperscripts || hasLaTeXCommand
}

/// Advanced preprocessing with proper currency handling
private func preprocessTextForRendering(_ text: String) -> String {
    print("🚨🚨🚨 LaTeX Preprocessing: Input: '\(text)'")

    // Step 0: FIRST normalize simple $NUMBER$ patterns to plain numbers
    // This handles cases like $250$, $10$, $20$ -> 250, 10, 20
    var processedText = normalizeSimpleNumberPatterns(text)
    print("🔧 LaTeX Preprocessing: After number normalization: '\(processedText)'")

    // Step 1: Check for LaTeX BEFORE processing to preserve original LaTeX commands
    let hasLaTeX = containsLaTeX(processedText)
    print("🔧 LaTeX Preprocessing: Text contains LaTeX: \(hasLaTeX)")

    // Step 2: Handle all currency and math patterns comprehensively
    processedText = handleAllMathAndCurrencyPatterns(processedText)
    print("🔧 LaTeX Preprocessing: After math/currency: '\(processedText)'")

    // Step 3: Handle newlines only if not in math context
    // Skip newline processing for math expressions to avoid breaking LaTeX
    if !hasLaTeX {
        processedText = handleNewlines(processedText)
        print("🔧 LaTeX Preprocessing: After newlines: '\(processedText)'")
    } else {
        print("🔧 LaTeX Preprocessing: Skipping newline processing for LaTeX content")
    }

    print("🔧 LaTeX Preprocessing: Final: '\(processedText)'")

    // Step 3: Check for specific patterns that might be problematic
    if text.contains("^") {
        print("🔧 LaTeX Preprocessing: Contains superscript character")
    }
    if text.contains("cm") || text.contains("m²") || text.contains("²") {
        print("🔧 LaTeX Preprocessing: Contains unit notation")
    }

    return processedText
}

/// Normalize simple $NUMBER$ patterns to plain numbers
/// Converts patterns like $250$, $10$, $20$ to 250, 10, 20
/// Also handles lists like $78, 85, 92, 70, 80$ -> 78, 85, 92, 70, 80
private func normalizeSimpleNumberPatterns(_ text: String) -> String {
    var result = text

    // STEP 1: Handle number lists first: $78, 85, 92, 70, 80$ -> 78, 85, 92, 70, 80
    let numberListPattern = #"\$(\d+(?:\.\d+)?(?:\s*,\s*\d+(?:\.\d+)?)*)\$"#

    let beforeListNormalization = result
    result = result.replacingOccurrences(
        of: numberListPattern,
        with: "$1",
        options: .regularExpression
    )

    if beforeListNormalization != result {
        print("🔧 Number list normalization: '\(beforeListNormalization)' -> '\(result)'")
    }

    // STEP 2: Handle individual numbers: $250$, $10$, $20.5$ -> 250, 10, 20.5
    let simpleNumberPattern = #"\$(\d+(?:\.\d+)?)\$"#

    // FIXED: Instead of checking the entire text for complex math,
    // check each individual $...$ pattern to see if it's a simple number
    // This allows us to convert $3$ and $45$ even if the text contains commas

    let beforeNormalization = result

    // Use NSRegularExpression to find and replace each match individually
    do {
        let regex = try NSRegularExpression(pattern: simpleNumberPattern, options: [])
        let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: result.count))

        // Process matches in reverse order to avoid index shifting
        for match in matches.reversed() {
            if let range = Range(match.range, in: result),
               let numberRange = Range(match.range(at: 1), in: result) {
                let fullMatch = String(result[range])  // e.g., "$250$"
                let numberOnly = String(result[numberRange])  // e.g., "250"

                // Check if this specific match is a simple number (no variables, operations, etc.)
                let isSimpleNumber = !fullMatch.contains("\\") &&  // No LaTeX commands
                                   !fullMatch.contains("+") &&    // No operations
                                   !fullMatch.contains("-") &&    // No operations
                                   !fullMatch.contains("*") &&
                                   !fullMatch.contains("/") &&
                                   !fullMatch.contains("^") &&    // No exponents
                                   !fullMatch.contains("_") &&    // No subscripts
                                   !fullMatch.contains("=") &&
                                   !fullMatch.contains("x") &&    // No variables
                                   !fullMatch.contains("y") &&
                                   !fullMatch.contains("z") &&
                                   !fullMatch.contains("n") &&
                                   !fullMatch.contains("(") &&    // No parentheses
                                   !fullMatch.contains(")")

                if isSimpleNumber {
                    result.replaceSubrange(range, with: numberOnly)
                    print("🔧 Converted simple number: '\(fullMatch)' -> '\(numberOnly)'")
                }
            }
        }
    } catch {
        print("🚨 Error in simple number normalization: \(error)")
        // Fallback to simple string replacement
        result = result.replacingOccurrences(
            of: simpleNumberPattern,
            with: "$1",
            options: .regularExpression
        )
    }

    if beforeNormalization != result {
        print("🔧 Simple number normalization: '\(beforeNormalization)' -> '\(result)'")
    }

    return result
}

/// Comprehensive handling of all math and currency patterns
private func handleAllMathAndCurrencyPatterns(_ text: String) -> String {
    print("🔧 handleAllMathAndCurrencyPatterns input: '\(text)'")
    var result = text

    // Normalize LaTeX line breaks for consistent spacing
    result = normalizeLaTeXLineBreaks(result)

    // PRIORITY FIX: Handle the specific $\$NUMBER$ format FIRST to prevent aggressive parsing
    // This must be done before any other pattern matching to avoid the issue where
    // "$\$10$ each and child tickets cost $\$5$" gets incorrectly parsed as LaTeX
    //
    // Examples of what this fixes:
    // - "$\$10$" -> "$10" (double backslash - original format)
    // - "$\$5$" -> "$5" (single backslash - after normalizeLaTeX)
    // - "$\$240$" -> "$240"
    // - "$\$15.50$" -> "$15.50"
    //
    // This prevents the LaTeX renderer from trying to render parts like
    // "$10$ each and child tickets cost $" as mathematical expressions

    // Handle both double backslash (original) and single backslash (after normalizeLaTeX)
    // More specific pattern that only matches escaped dollar signs, not LaTeX commands
    let specificEscapedDollarPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#
    let beforeSpecific = result
    result = result.replacingOccurrences(
        of: specificEscapedDollarPattern,
        with: "$$1",
        options: .regularExpression
    )
    if result != beforeSpecific {
        print("🔧 handleAllMathAndCurrencyPatterns: PRIORITY specific escaped dollar conversion: '\(beforeSpecific)' -> '\(result)'")
    }

    // Handle other escaped dollar patterns: $\$120$ -> $120, $\$8,000$ -> $8,000, $\$3$ -> $3
    // Also handle patterns with units: $\$12/kg$ -> $12/kg
    // Updated pattern to handle more cases including $\$800$ and $\$50$
    // Now also handles double backslash patterns like $\\$15$ -> $15
    // More specific pattern that only matches escaped dollar signs
    let escapedDollarPattern = #"\$\\\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(/[a-zA-Z]+)?\$"#
    let beforeEscaped = result
    result = result.replacingOccurrences(
        of: escapedDollarPattern,
        with: "$$1$2",
        options: .regularExpression
    )
    if result != beforeEscaped {
        print("🔧 handleAllMathAndCurrencyPatterns: Escaped dollar conversion: '\(beforeEscaped)' -> '\(result)'")
    }

    // Additional pattern to handle cases where the escaped dollar pattern might not match
    // Handle patterns like $\$800$ -> $800 and $\\$15$ -> $15 more aggressively
    let additionalEscapedPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#
    let beforeAdditional = result
    result = result.replacingOccurrences(
        of: additionalEscapedPattern,
        with: "$$1",
        options: .regularExpression
    )
    if result != beforeAdditional {
        print("🔧 handleAllMathAndCurrencyPatterns: Additional escaped dollar conversion: '\(beforeAdditional)' -> '\(result)'")
    }
    
    // Handle pure numeric dollar amounts ONLY in clear currency contexts
    // This pattern matches $NUMBER$ where NUMBER is purely numeric (with optional negative and decimal)
    // BUT we need to be much more careful about when to apply this conversion
    let pureNumberPattern = #"\$(-?\d+(?:\.\d+)?)\$"#
    let beforeCurrency = result

    // FIXED: Pure numbers like $400$, $50$, $8$ should ALWAYS be converted to plain numbers
    // regardless of context, because they represent concrete values, not mathematical expressions.
    // Only skip conversion for actual mathematical expressions with variables, operations, etc.

    let shouldSkipConversion = result.contains("(") ||
                              result.contains(",") ||
                              result.contains(")") ||
                              result.contains("x") ||  // Variables like $x$, $y$
                              result.contains("y") ||
                              result.contains("z") ||
                              result.contains("n") ||
                              result.contains("\\") ||  // LaTeX commands like $\frac{1}{2}$
                              result.contains("^") ||   // Exponents like $2^3$
                              result.contains("_") ||   // Subscripts
                              result.contains("+") ||   // Mathematical operations
                              (result.contains("-") && !result.hasPrefix("$-")) ||   // Operations but allow negative numbers like $-5$
                              result.contains("*") ||
                              result.contains("/") ||
                              result.contains("=") ||
                              result.contains("°") ||   // Degrees
                              result.contains("cm") ||  // Units
                              result.contains("mm") ||
                              result.contains("m²") ||
                              result.contains("²") ||
                              result.contains("³")

    if !shouldSkipConversion {
        print("🔧 Attempting pure number conversion on: '\(result)'")
        print("🔧 Pattern: \(pureNumberPattern)")

        // Test if the pattern matches
        let regex = try! NSRegularExpression(pattern: pureNumberPattern, options: [])
        let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: result.count))
        print("🔧 Found \(matches.count) matches")

        for match in matches {
            if let range = Range(match.range, in: result) {
                print("🔧 Match found: '\(result[range])'")
            }
        }

        let testResult = result.replacingOccurrences(
            of: pureNumberPattern,
            with: "$1",
            options: .regularExpression
        )
        print("🔧 Pattern test result: '\(testResult)'")

        result = testResult
        print("🔧 After pure number conversion: '\(result)'")
    } else {
        print("🔧 Skipping pure number conversion for: '\(result)' (mathematical context detected)")
    }

    if beforeCurrency != result {
        print("🔧 Pure number conversion: '\(beforeCurrency)' -> '\(result)'")
    }
    
    // Handle other escaped currency symbols: $\€300$ -> €300, $\€8,000$ -> €8,000
    let otherCurrencyPattern = #"\$\\([€£¥¢])(?![a-zA-Z])(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(?![a-zA-Z])\$"#
    result = result.replacingOccurrences(
        of: otherCurrencyPattern,
        with: "$1$2",
        options: .regularExpression
    )
    
    // Handle percentage values: $25\%$ -> 25%, $1,600\%$ -> 1,600%, $5\\%$ -> 5%
    let percentagePattern = #"\$(?![a-zA-Z])(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(?![a-zA-Z])\\%\$"#
    result = result.replacingOccurrences(
        of: percentagePattern,
        with: "$1%",
        options: .regularExpression
    )

    print("🔧 handleAllMathAndCurrencyPatterns output: '\(result)'")
    return result
}

/// Normalize LaTeX line breaks to ensure consistent spacing
private func normalizeLaTeXLineBreaks(_ text: String) -> String {
    let beforeNormalization = text
    var result = text

    // Use regex to replace any sequence of 3 or more backslashes with just \\
    do {
        let multipleBackslashPattern = #"\\{3,}"#
        let regex = try NSRegularExpression(pattern: multipleBackslashPattern, options: [])
        result = regex.stringByReplacingMatches(in: result, options: [], range: NSRange(result.startIndex..., in: result), withTemplate: "\\\\\\\\")

        // Handle cases with spaces around multiple backslashes
        result = result.replacingOccurrences(of: "\\\\ \\\\", with: "\\\\")
        result = result.replacingOccurrences(of: "\\\\\\\\ ", with: "\\\\ ")
        result = result.replacingOccurrences(of: " \\\\\\\\", with: " \\\\")
        result = result.replacingOccurrences(of: " \\\\\\\\ ", with: " \\\\ ")

        // Handle specific common patterns for equation formatting using regex
        let equationPattern = #"(=\s*\d+)\s*\\{4,}\s*(\d+[A-Z])"#
        let equationRegex = try NSRegularExpression(pattern: equationPattern, options: [])
        result = equationRegex.stringByReplacingMatches(in: result, options: [], range: NSRange(result.startIndex..., in: result), withTemplate: "$1 \\\\\\\\ $2")

        if result != beforeNormalization {
            print("🔧 normalizeLaTeXLineBreaks: '\(beforeNormalization)' -> '\(result)'")
        }
    } catch {
        // Fallback to simple replacements if regex fails
        result = result.replacingOccurrences(of: "\\\\\\\\", with: "\\\\")
        result = result.replacingOccurrences(of: "\\\\\\", with: "\\\\")
        print("🔧 normalizeLaTeXLineBreaks: Regex failed, using fallback normalization")
    }

    return result
}

/// Handle newline patterns - but preserve LaTeX formatting
private func handleNewlines(_ text: String) -> String {
    // Only convert actual newline characters, not LaTeX commands
    // Don't convert \n when it's part of LaTeX (like in math expressions)
    var result = text

    // Only convert literal newline characters, not escaped ones in LaTeX
    result = result.replacingOccurrences(of: "\r\n", with: "\n")
    result = result.replacingOccurrences(of: "\r", with: "\n")

    // Don't convert literal \n to actual newlines if text contains math expressions
    // This prevents breaking the layout when \n appears between math expressions
    if !text.contains("$") && !text.contains("$$") {
        result = result.replacingOccurrences(of: "\\n", with: "\n")
        result = result.replacingOccurrences(of: "\\r\\n", with: "\n")
    } else {
        // If text contains math, treat \n as literal text, not line breaks
        // This preserves the intended layout for math expressions
        print("🔧 Preserving literal \\n in math context: '\(text)'")
    }

    return result
}

/// Quick check for LaTeX content (simplified version for preprocessing)
private func containsLaTeX(_ text: String) -> Bool {
    return text.contains("$") ||
           text.contains("\\frac") ||
           text.contains("\\sqrt") ||
           text.contains("\\times") ||
           text.contains("\\div") ||
           text.contains("\\alpha") ||
           text.contains("\\beta") ||
           text.contains("\\gamma") ||
           text.contains("\\theta") ||
           text.contains("\\pi") ||
           text.contains("\\sum") ||
           text.contains("\\int") ||
           text.contains("\\leq") ||
           text.contains("\\geq") ||
           text.contains("\\neq") ||
           text.contains("\\angle") ||
           text.contains("\\circ") ||
           text.contains("\\perp") ||
           text.contains("\\parallel") ||
           text.contains("^")
}

/// Advanced regex replacement with proper group handling
private func replaceAllMatches(in text: String, pattern: String, replacement: (String, [String]) -> String) -> String {
    do {
        let regex = try NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(text.startIndex..<text.endIndex, in: text)
        let matches = regex.matches(in: text, options: [], range: range)
        
        var result = text
        
        // Process matches in reverse order to avoid index shifting
        for match in matches.reversed() {
            guard let matchRange = Range(match.range, in: result) else { continue }
            
            let matchedText = String(result[matchRange])
            var groups: [String] = []
            
            // Extract capture groups
            for i in 1..<match.numberOfRanges {
                if let groupRange = Range(match.range(at: i), in: result) {
                    groups.append(String(result[groupRange]))
                }
            }
            
            let replacementText = replacement(matchedText, groups)
            result.replaceSubrange(matchRange, with: replacementText)
        }
        
        return result
    } catch {
        print("❌ Regex error for pattern \(pattern): \(error)")
        return text
    }
}

/// Fix double backslashes in LaTeX commands to single backslashes
private func fixDoubleBackslashesInLaTeX(_ text: String) -> String {
    var fixedText = text

    // List of common LaTeX commands that might have double backslashes
    let latexCommands = [
        "\\\\frac", "\\\\sqrt", "\\\\times", "\\\\div", "\\\\cdot", "\\\\pm", "\\\\mp",
        "\\\\leq", "\\\\geq", "\\\\neq", "\\\\approx", "\\\\equiv", "\\\\cong", "\\\\sim",
        "\\\\alpha", "\\\\beta", "\\\\gamma", "\\\\delta", "\\\\epsilon", "\\\\zeta",
        "\\\\eta", "\\\\theta", "\\\\iota", "\\\\kappa", "\\\\lambda", "\\\\mu",
        "\\\\nu", "\\\\xi", "\\\\pi", "\\\\rho", "\\\\sigma", "\\\\tau", "\\\\upsilon",
        "\\\\phi", "\\\\chi", "\\\\psi", "\\\\omega",
        "\\\\sin", "\\\\cos", "\\\\tan", "\\\\sec", "\\\\csc", "\\\\cot",
        "\\\\log", "\\\\ln", "\\\\exp", "\\\\lim", "\\\\max", "\\\\min",
        "\\\\sum", "\\\\prod", "\\\\int", "\\\\oint", "\\\\partial", "\\\\nabla",
        "\\\\infty", "\\\\angle", "\\\\circ", "\\\\degree", "\\\\perp", "\\\\parallel",
        "\\\\triangle", "\\\\square", "\\\\diamond", "\\\\star",
        "\\\\cup", "\\\\cap", "\\\\subset", "\\\\supset", "\\\\subseteq", "\\\\supseteq",
        "\\\\in", "\\\\notin", "\\\\ni", "\\\\emptyset", "\\\\varnothing",
        "\\\\forall", "\\\\exists", "\\\\nexists", "\\\\land", "\\\\lor", "\\\\neg",
        "\\\\implies", "\\\\iff", "\\\\models", "\\\\vdash", "\\\\dashv",
        "\\\\leftarrow", "\\\\rightarrow", "\\\\leftrightarrow", "\\\\uparrow", "\\\\downarrow",
        "\\\\Leftarrow", "\\\\Rightarrow", "\\\\Leftrightarrow", "\\\\Uparrow", "\\\\Downarrow",
        "\\\\left", "\\\\right", "\\\\big", "\\\\Big", "\\\\bigg", "\\\\Bigg",
        "\\\\text", "\\\\mathrm", "\\\\mathbf", "\\\\mathit", "\\\\mathcal", "\\\\mathbb",
        "\\\\begin", "\\\\end", "\\\\over", "\\\\choose", "\\\\binom",
        "\\\\displaystyle", "\\\\textstyle", "\\\\scriptstyle", "\\\\scriptscriptstyle"
    ]

    // Replace double backslashes with single backslashes for LaTeX commands
    for command in latexCommands {
        let singleBackslashCommand = command.replacingOccurrences(of: "\\\\", with: "\\")
        fixedText = fixedText.replacingOccurrences(of: command, with: singleBackslashCommand)
    }

    // Also handle generic pattern for any remaining double backslashes before letters
    // This catches any LaTeX commands we might have missed
    do {
        let regex = try NSRegularExpression(pattern: #"\\\\([a-zA-Z]+)"#, options: [])
        fixedText = regex.stringByReplacingMatches(
            in: fixedText,
            options: [],
            range: NSRange(location: 0, length: fixedText.count),
            withTemplate: "\\\\$1"
        )
    } catch {
        print("❌ Error in fixDoubleBackslashesInLaTeX: \(error)")
    }

    // Handle cases where there might be triple or more backslashes
    // Reduce any sequence of multiple backslashes to single backslash before LaTeX commands
    do {
        let regex = try NSRegularExpression(pattern: #"\\{2,}([a-zA-Z]+)"#, options: [])
        fixedText = regex.stringByReplacingMatches(
            in: fixedText,
            options: [],
            range: NSRange(location: 0, length: fixedText.count),
            withTemplate: "\\\\$1"
        )
    } catch {
        print("❌ Error in fixDoubleBackslashesInLaTeX: \(error)")
    }

    return fixedText
}

// MARK: - Math Baseline Alignment Modifier
struct MathBaselineAlignment: ViewModifier {
    var offset: CGFloat = 6 // Default offset for baseline alignment
    func body(content: Content) -> some View {
        content
            .alignmentGuide(.firstTextBaseline) { d in
                d[VerticalAlignment.center] + offset
            }
    }
}
extension View {
    func mathBaselineAligned(offset: CGFloat = 6) -> some View {
        self.modifier(MathBaselineAlignment(offset: offset))
    }
}

// MARK: - Enhanced Question Text Renderer with SwiftMath
struct QuestionTextRenderer: View {
    let text: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    init(text: String, fontSize: CGFloat = 16) {
        self.text = text
        self.fontSize = fontSize
    }

    var body: some View {
        // Preprocess text to handle currency, math, and newlines properly FIRST
        let processedText = preprocessTextForRendering(text)
        let _ = print("🚨🚨🚨 QuestionTextRenderer: Original text: '\(text)'")
        let _ = print("🚨🚨🚨 QuestionTextRenderer: Processed text: '\(processedText)'")

        // Check if PROCESSED text contains LaTeX expressions (after number conversion)
        let hasLaTeX = containsLaTeX(processedText)
        let _ = print("🚨🚨🚨 QuestionTextRenderer: Processed text contains LaTeX: \(hasLaTeX)")

        // Check if text contains LaTeX expressions
        if hasLaTeX {
            // Pass the already processed text to avoid double processing
            SwiftMathQuestionView(text: processedText, fontSize: fontSize, skipPreprocessing: true)
        } else if isStructuredQuestion(processedText) {
            StructuredQuestionView(text: processedText, fontSize: fontSize)
        } else {
            // Plain text without LaTeX
            Text(processedText)
                .font(.system(size: fontSize))
                .foregroundColor(colorScheme == .dark ? .white : .black)
                .frame(maxWidth: .infinity, alignment: .leading)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    // Check if text contains LaTeX expressions
    private func containsLaTeX(_ text: String) -> Bool {
        var textToCheck = text

        // Check if this is a mathematical context before removing simple numbers
        let mathKeywords = ["area", "trapezium", "parallel", "perpendicular", "height", "measuring", "measured", "measurement", "cm", "mm", "meters", "meter", "angle", "triangle", "circle", "radius", "diameter", "perimeter", "volume", "surface", "degrees", "°", "number", "equal", "times", "decreased", "increased", "find", "solve", "equation", "expression", "value", "calculate", "error", "absolute", "length", "actual"]
        let containsMathKeywords = mathKeywords.contains { text.lowercased().contains($0) }

        // Only remove simple number patterns from LaTeX detection if NOT in a mathematical context
        if !containsMathKeywords && !text.contains("°") && !text.contains("cm") && !text.contains("mm") {
            // Remove simple numbers like $8$, $12$, $5$, $360$, $90$, etc. but NOT variables like $x$, $y$
            let simpleNumberPattern = #"\$\d+\$"#  // Simple pattern for pure numbers
            let decimalNumberPattern = #"\$\d+\.\d+\$"#  // Pattern for decimal numbers

            // PRIORITY: Handle the specific $\$NUMBER$ format FIRST (both single and double backslash)
            let specificEscapedDollarPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#

            let escapedDollarPattern = #"\$\\\$\d+(?:\.\d+)?\$"#  // Pattern for escaped dollars with optional decimals

            // Also remove percentage patterns like $25\%$, $15\%$, etc.
            let percentagePattern = #"\$\d+\\%\$"#

            textToCheck = textToCheck.replacingOccurrences(
                of: simpleNumberPattern,
                with: "",
                options: .regularExpression
            )
            textToCheck = textToCheck.replacingOccurrences(
                of: decimalNumberPattern,
                with: "",
                options: .regularExpression
            )
            textToCheck = textToCheck.replacingOccurrences(
                of: specificEscapedDollarPattern,
                with: "",
                options: .regularExpression
            )
            textToCheck = textToCheck.replacingOccurrences(
                of: escapedDollarPattern,
                with: "",
                options: .regularExpression
            )
            textToCheck = textToCheck.replacingOccurrences(
                of: percentagePattern,
                with: "",
                options: .regularExpression
            )
        } else {
            print("🔧 containsLaTeX: Mathematical context detected, preserving simple numbers as LaTeX")
        }

        // Remove pure currency amounts like $20, $28.50, $-35 etc.
        // BUT preserve coordinate pairs, expressions with parentheses, commas, or letters
        if !textToCheck.contains("(") && !textToCheck.contains(",") && !textToCheck.contains(")") &&
           !textToCheck.contains(where: { $0.isLetter }) {
            let regularCurrencyPattern = #"\$-?\d+(?:\.\d+)?\$"#
            textToCheck = textToCheck.replacingOccurrences(
                of: regularCurrencyPattern,
                with: "",
                options: .regularExpression
            ).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // Only treat as LaTeX if there are actual LaTeX expressions after removing currency
        let hasInlineMath = textToCheck.range(of: #"\$[^$]*\\[a-zA-Z]+[^$]*\$"#, options: .regularExpression) != nil
        let hasDisplayMath = textToCheck.range(of: #"\$\$[^$]+\$\$"#, options: .regularExpression) != nil

        // Check for mathematical expressions that should be rendered as LaTeX
        let hasMathExpressions =
            // Single variables: $x$, $y$, $A$ (letters only)
            textToCheck.range(of: #"\$[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
            // Variables with coefficients: $3x$, $10y$ (must contain at least one letter)
            textToCheck.range(of: #"\$\d+[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
            // Algebraic equations starting with numbers: $3n = 72$, $2x + 5 = 10$
            textToCheck.range(of: #"\$\d+[a-zA-Z]+[^$]*[=+\-*/][^$]*\$"#, options: .regularExpression) != nil ||
            // Algebraic expressions with operators and variables: $3x + 10y$, $x - 2y$
            textToCheck.range(of: #"\$[^$]*[a-zA-Z]+[^$]*[+\-*/][^$]*\$"#, options: .regularExpression) != nil ||
            // Simple expressions with operators: $x + y$, $a - b$
            textToCheck.range(of: #"\$[a-zA-Z]+\s*[=+\-*/]\s*[a-zA-Z0-9]+\$"#, options: .regularExpression) != nil ||
            // Coordinate pairs: $(4, 3)$, $(-4, 3)$, $(x, y)$
            textToCheck.range(of: #"\$\([^$)]*,\s*[^$)]*\)\$"#, options: .regularExpression) != nil ||
            // Mathematical expressions with parentheses: $(x + 2)$, $(-3 + 4)$, $(-2 + 3) = 1$
            textToCheck.range(of: #"\$\([^$)]*[+\-*/][^$)]*\)[^$]*\$"#, options: .regularExpression) != nil ||
            // Multiple parentheses expressions: $2(-2)(3) = -12$, $(1)^2 - 2(-2)(3)$
            textToCheck.range(of: #"\$[^$]*\([^$)]*\)[^$]*\([^$)]*\)[^$]*\$"#, options: .regularExpression) != nil ||
            // Expressions with equals sign: $1 - (-12)$, $1 + 12 = 13$
            textToCheck.range(of: #"\$[^$]*[=][^$]*\$"#, options: .regularExpression) != nil ||
            // Expressions with negative numbers in parentheses: $1 - (-12)$
            textToCheck.range(of: #"\$[^$]*\(-\d+[^$)]*\)[^$]*\$"#, options: .regularExpression) != nil ||
            // Simple numbers in mathematical contexts (like geometry problems)
            (containsMathKeywords && textToCheck.range(of: #"\$\d+\$"#, options: .regularExpression) != nil)
            // Note: Removed escaped dollar sign detection here since these should be treated as currency, not LaTeX

        // Check for superscript patterns (like cm^2, m^3, etc.)
        let hasSuperscripts = textToCheck.range(of: #"\$[^$]*\^[^$]*\$"#, options: .regularExpression) != nil ||
                             textToCheck.contains("^2") ||
                             textToCheck.contains("^3") ||
                             textToCheck.contains("^{") ||
                             textToCheck.contains("^\\")

        print("🔧 containsLaTeX: textToCheck = '\(textToCheck)'")
        print("🔧 containsLaTeX: hasInlineMath = \(hasInlineMath)")
        print("🔧 containsLaTeX: hasDisplayMath = \(hasDisplayMath)")
        print("🔧 containsLaTeX: hasMathExpressions = \(hasMathExpressions)")
        print("🔧 containsLaTeX: hasSuperscripts = \(hasSuperscripts)")

        // Enhanced debugging
        if textToCheck.contains("$-") {
            print("🔧 containsLaTeX: Contains negative dollar pattern: '\(textToCheck)'")
            print("🔧 containsLaTeX: hasMathExpressions = \(hasMathExpressions)")
        }
        if textToCheck.contains("x") || textToCheck.contains("y") || textToCheck.contains("(") {
            print("🔧 containsLaTeX: Contains math symbols: '\(textToCheck)'")
            print("🔧 containsLaTeX: hasMathExpressions = \(hasMathExpressions)")
        }

        // Get the LaTeX command detection (defined later in the function)
        let hasLaTeXCommand = textToCheck.contains("\\frac") ||
                              textToCheck.contains("\\sqrt") ||
                              textToCheck.contains("\\times") ||
                              textToCheck.contains("\\div") ||
                              textToCheck.contains("\\alpha") ||
                              textToCheck.contains("\\beta") ||
                              textToCheck.contains("\\gamma") ||
                              textToCheck.contains("\\theta") ||
                              textToCheck.contains("\\pi") ||
                              textToCheck.contains("\\sum") ||
                              textToCheck.contains("\\int") ||
                              textToCheck.contains("\\leq") ||
                              textToCheck.contains("\\geq") ||
                              textToCheck.contains("\\neq") ||
                              textToCheck.contains("\\angle") ||
                              textToCheck.contains("\\circ") ||
                              textToCheck.contains("\\perp") ||
                              textToCheck.contains("\\parallel") ||
                              // Also check the original text for LaTeX commands
                              text.contains("\\neq") ||
                              text.contains("\\leq") ||
                              text.contains("\\geq") ||
                              text.contains("\\frac") ||
                              text.contains("\\sqrt")

        // Special check: if after removing all currency and percentage patterns,
        // the text is empty or only contains whitespace, then it's not LaTeX
        let cleanedText = textToCheck.trimmingCharacters(in: .whitespacesAndNewlines)
        if cleanedText.isEmpty {
            print("🔧 containsLaTeX: Text only contains currency/percentage patterns, treating as plain text")
            return false
        }

        // Debug the final decision
        let finalResult = hasInlineMath || hasDisplayMath || hasMathExpressions || hasSuperscripts || hasLaTeXCommand
        print("🔧 containsLaTeX final decision for '\(text)': \(finalResult)")
        print("   - textToCheck after cleanup: '\(textToCheck)'")
        print("   - hasInlineMath: \(hasInlineMath)")
        print("   - hasDisplayMath: \(hasDisplayMath)")
        print("   - hasMathExpressions: \(hasMathExpressions)")
        print("   - hasSuperscripts: \(hasSuperscripts)")
        print("   - hasLaTeXCommand: \(hasLaTeXCommand)")

        return hasInlineMath || hasDisplayMath || hasMathExpressions || hasSuperscripts || hasLaTeXCommand
    }
    
    // Check if the text is in a mathematical context (for determining if simple numbers should be treated as math)
    private func isMathematicalContext(_ text: String) -> Bool {
        let mathKeywords = ["area", "trapezium", "parallel", "perpendicular", "height", "measuring", "measured", "measurement", "cm", "mm", "meters", "meter", "angle", "triangle", "circle", "radius", "diameter", "perimeter", "volume", "surface", "degrees", "°", "number", "equal", "times", "decreased", "increased", "find", "solve", "equation", "expression", "value", "calculate", "error", "absolute", "length", "actual"]
        return mathKeywords.contains { text.lowercased().contains($0) } || text.contains("°") || text.contains("cm") || text.contains("mm")
    }
}

// MARK: - SwiftMath Question View
struct SwiftMathQuestionView: View {
    let text: String
    let fontSize: CGFloat
    let skipPreprocessing: Bool
    @Environment(\.colorScheme) private var colorScheme

    init(text: String, fontSize: CGFloat, skipPreprocessing: Bool = false) {
        self.text = text
        self.fontSize = fontSize
        self.skipPreprocessing = skipPreprocessing
    }

    var body: some View {
        // Preprocess text to handle newlines properly (unless already processed)
        let processedText = skipPreprocessing ? text : preprocessTextForRendering(text)

        // Check if we have mixed content (text + math) that should flow inline
        let segments = parseTextWithMath(processedText)
        let hasDisplayMath = segments.contains { $0.isLaTeX && $0.isDisplayMode }

        if hasDisplayMath {
            // Smart layout: group inline content together, separate display math
            SmartDisplayMathView(segments: segments, fontSize: fontSize)
        } else if segments.count > 1 {
            // Multiple segments with inline math - use flexible layout
            FlexibleInlineView(segments: segments, fontSize: fontSize)
        } else {
            // Single segment
            let segment = segments.first!
            if segment.isLaTeX {
                Group {
                    if segment.isDisplayMode {
                        SwiftMathView(
                            latex: segment.content,
                            fontSize: fontSize,
                            displayMode: segment.isDisplayMode
                        )
                        .padding(.bottom, 0)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    } else {
                        SwiftMathView(
                            latex: segment.content,
                            fontSize: fontSize,
                            displayMode: segment.isDisplayMode
                        )
                        .padding(.bottom, fontSize * 0.2)
                        .mathBaselineAligned(offset: 6)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
            } else {
                Text(segment.content)
                    .font(.system(size: fontSize))
                    .foregroundColor(colorScheme == .dark ? .white : .black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }

    // Parse text into segments of plain text and LaTeX
    private func parseTextWithMath(_ text: String) -> [TextSegment] {
        var segments: [TextSegment] = []
        var currentText = text

        // Handle literal \n more carefully:
        // - Replace \n with space only when it's between text and inline math
        // - Keep \n as line breaks when it's before/after display math ($$...$$)

        // First, let's identify display math blocks and preserve line breaks around them
        let displayMathPattern = #"\$\$[^$]+\$\$"#
        let hasDisplayMath = currentText.range(of: displayMathPattern, options: .regularExpression) != nil

        if hasDisplayMath {
            // For display math, be more conservative with line breaks
            // Only convert \n to line breaks if they separate different logical sections
            // For equations within the same option, use smaller spacing
            currentText = currentText.replacingOccurrences(of: "\\n", with: " ")
        } else {
            // For inline math only, replace \n with spaces
            currentText = currentText.replacingOccurrences(of: "\\n", with: " ")
        }

        // Handle inline math ($...$) and display math ($$...$$)
        // Be more precise about what constitutes math to avoid matching currency amounts

        // Pattern for inline math - only match if it contains mathematical content:
        // - Variables: $x$, $A$, $y$ (single letters)
        // - Expressions with operators: $x + y$, $3x - 2$
        // - Expressions with mathematical symbols: $\frac{1}{2}$, $\sqrt{x}$
        // - Coordinate pairs: $(x, y)$, $(3, 4)$
        // - Expressions with parentheses: $(x + 2)$
        // - Simple numbers in mathematical contexts: $10$, $35$
        let inlineMathPattern = isMathematicalContext(text) ? 
            #"\$(?:[a-zA-Z]+|[^$]*[a-zA-Z]+[^$]*[+\-*/=][^$]*|[^$]*\\[a-zA-Z]+[^$]*|\([^$)]*,\s*[^$)]*\)|\([^$)]*[+\-*/][^$)]*\)|[^$]*\^[^$]*|\d+(?:\.\d+)?)\$"# :
            #"\$(?:[a-zA-Z]+|[^$]*[a-zA-Z]+[^$]*[+\-*/=][^$]*|[^$]*\\[a-zA-Z]+[^$]*|\([^$)]*,\s*[^$)]*\)|\([^$)]*[+\-*/][^$)]*\)|[^$]*\^[^$]*)\$"#

        // Create a combined pattern to handle both display math and inline math
        // Also handle incomplete display math ($$...$ at end of string)
        let displayMathPart = #"(\$\$[^$]+\$\$|\$\$[^$]+\$(?=\s*$))"#
        let combinedPattern = displayMathPart + "|(" + inlineMathPattern + ")"

        do {
            let regex = try NSRegularExpression(pattern: combinedPattern, options: [])
            let matches = regex.matches(in: currentText, options: [], range: NSRange(currentText.startIndex..., in: currentText))

            print("🔧 parseTextWithMath: Input text: '\(currentText)'")
            print("🔧 parseTextWithMath: Found \(matches.count) matches")

            var lastEnd = currentText.startIndex

            for (matchIndex, match) in matches.enumerated() {
                guard let range = Range(match.range, in: currentText) else { continue }
                print("🔧 parseTextWithMath: Processing match \(matchIndex): range \(match.range)")

                // Add text before the math
                let beforeMath = String(currentText[lastEnd..<range.lowerBound])
                print("🔧 parseTextWithMath: beforeMath: '\(beforeMath)'")
                if !beforeMath.isEmpty {
                    // Clean up the text but preserve natural spacing
                    // Only convert newlines to spaces if they're not at sentence boundaries
                    let cleaned = cleanTextForInlineFlow(beforeMath)
                    if !cleaned.isEmpty {
                        print("🔧 parseTextWithMath: Adding text segment: '\(cleaned)'")
                        segments.append(TextSegment(content: cleaned, isLaTeX: false, isDisplayMode: false))
                    }
                }

                // Extract the math content
                let mathContent = String(currentText[range])
                let isDisplayMath = mathContent.hasPrefix("$$")

                let latexContent: String
                if isDisplayMath {
                    if mathContent.hasSuffix("$$") {
                        // Complete display math: $$...$$
                        latexContent = String(mathContent.dropFirst(2).dropLast(2))
                    } else {
                        // Incomplete display math: $$...$ (missing one $ at end)
                        latexContent = String(mathContent.dropFirst(2).dropLast(1))
                    }
                } else {
                    // Inline math: $...$
                    latexContent = String(mathContent.dropFirst().dropLast())
                }

                print("🔧 parseTextWithMath: mathContent: '\(mathContent)' -> latexContent: '\(latexContent)' (displayMode: \(isDisplayMath))")

                // Only add non-empty LaTeX content
                let trimmedLatex = latexContent.trimmingCharacters(in: .whitespacesAndNewlines)
                if !trimmedLatex.isEmpty && trimmedLatex != " " && trimmedLatex.count > 0 {
                    segments.append(TextSegment(content: trimmedLatex, isLaTeX: true, isDisplayMode: isDisplayMath))
                } else {
                    print("🔧 parseTextWithMath: Skipping empty/whitespace LaTeX content: '\(latexContent)'")
                }

                lastEnd = range.upperBound
            }

            // Add any remaining text
            let remainingText = String(currentText[lastEnd...])
            if !remainingText.isEmpty {
                // Clean up the text but preserve natural spacing
                let cleaned = cleanTextForInlineFlow(remainingText)
                if !cleaned.isEmpty {
                    segments.append(TextSegment(content: cleaned, isLaTeX: false, isDisplayMode: false))
                }
            }

        } catch {
            // Fallback: if regex fails, treat as potential LaTeX
            segments.append(TextSegment(content: text, isLaTeX: true, isDisplayMode: false))
        }

        // If no segments were created, treat the entire text as potential LaTeX
        if segments.isEmpty {
            segments.append(TextSegment(content: text, isLaTeX: true, isDisplayMode: false))
        }

        // Debug output
        print("🔧 Final segments for text: '\(text)'")
        for (index, segment) in segments.enumerated() {
            print("🔧 Segment \(index): '\(segment.content)' (isLaTeX: \(segment.isLaTeX), displayMode: \(segment.isDisplayMode))")
        }

        return segments
    }

    // Helper function to clean text for inline flow without breaking natural sentence structure
    private func cleanTextForInlineFlow(_ text: String) -> String {
        var cleaned = text

        // Replace multiple spaces with single space
        cleaned = cleaned.replacingOccurrences(of: #"\s+"#, with: " ", options: .regularExpression)

        // Only convert newlines to spaces if they're not at natural sentence boundaries
        // Keep newlines that come after periods, exclamation marks, or question marks
        let lines = cleaned.components(separatedBy: .newlines)
        var result = ""

        for (index, line) in lines.enumerated() {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmedLine.isEmpty {
                if index > 0 {
                    // Check if previous line ended with sentence-ending punctuation
                    let previousLine = lines[index - 1].trimmingCharacters(in: .whitespacesAndNewlines)
                    if previousLine.hasSuffix(".") || previousLine.hasSuffix("!") || previousLine.hasSuffix("?") || previousLine.hasSuffix(":") {
                        result += " " // Add space after sentence-ending punctuation
                    } else {
                        result += " " // Add space for continuation
                    }
                }
                result += trimmedLine
            }
        }

        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // Check if the text is in a mathematical context (for determining if simple numbers should be treated as math)
    private func isMathematicalContext(_ text: String) -> Bool {
        let mathKeywords = ["area", "trapezium", "parallel", "perpendicular", "height", "measuring", "measured", "measurement", "cm", "mm", "meters", "meter", "angle", "triangle", "circle", "radius", "diameter", "perimeter", "volume", "surface", "degrees", "°", "number", "equal", "times", "decreased", "increased", "find", "solve", "equation", "expression", "value", "calculate", "error", "absolute", "length", "actual"]
        return mathKeywords.contains { text.lowercased().contains($0) } || text.contains("°") || text.contains("cm") || text.contains("mm")
    }
}

// MARK: - Smart Display Math View
struct SmartDisplayMathView: View {
    let segments: [TextSegment]
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        let groups = groupedSegments
        print("🔧 SmartDisplayMathView: \(groups.count) groups")
        for (index, group) in groups.enumerated() {
            print("🔧 Group \(index): isDisplayMath=\(group.isDisplayMath), segments=\(group.segments.count), content='\(group.content)'")
        }

        return VStack(alignment: .leading, spacing: 0) {
            // Group consecutive inline segments together
            ForEach(Array(groups.enumerated()), id: \.offset) { index, group in
                if group.isDisplayMath {
                    // Display math on its own line - only render if content is not empty
                    if !group.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        SwiftMathView(
                            latex: group.content,
                            fontSize: fontSize,
                            displayMode: true
                        )
                        .padding(.vertical, 0)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                } else {
                    // Inline content (text + inline math) on one line
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        ForEach(Array(group.segments.enumerated()), id: \.offset) { segIndex, segment in
                            // Only render non-empty segments
                            if !segment.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                                if segment.isLaTeX {
                                    SwiftMathView(
                                        latex: segment.content,
                                        fontSize: fontSize,
                                        displayMode: false
                                    )
                                    .fixedSize()
                                    .alignmentGuide(.firstTextBaseline) { d in
                                        // Align math baseline with text baseline
                                        // For inline math, align to the bottom minus a small offset
                                        d[.bottom] - 2
                                    }
                                } else {
                                    Text(segment.content)
                                        .font(.system(size: fontSize))
                                        .foregroundColor(colorScheme == .dark ? .white : .black)
                                        .fixedSize(horizontal: false, vertical: true)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // Group consecutive inline segments together
    private var groupedSegments: [SegmentGroup] {
        var groups: [SegmentGroup] = []
        var currentInlineSegments: [TextSegment] = []

        // Filter out empty segments first
        let filteredSegments = segments.filter { segment in
            let trimmed = segment.content.trimmingCharacters(in: .whitespacesAndNewlines)
            let shouldKeep = !trimmed.isEmpty && trimmed != "$" && trimmed != "$$"
            if !shouldKeep {
                print("🔧 SmartDisplayMathView: Filtering out segment: '\(segment.content)' (isLaTeX: \(segment.isLaTeX))")
            }
            return shouldKeep
        }

        for segment in filteredSegments {
            if segment.isLaTeX && segment.isDisplayMode {
                // Add any accumulated inline segments as a group
                if !currentInlineSegments.isEmpty {
                    groups.append(SegmentGroup(segments: currentInlineSegments, isDisplayMath: false, content: ""))
                    currentInlineSegments = []
                }
                // Add display math as its own group
                groups.append(SegmentGroup(segments: [], isDisplayMath: true, content: segment.content))
            } else {
                // Accumulate inline segments (text or inline math)
                currentInlineSegments.append(segment)
            }
        }

        // Add any remaining inline segments
        if !currentInlineSegments.isEmpty {
            groups.append(SegmentGroup(segments: currentInlineSegments, isDisplayMath: false, content: ""))
        }

        return groups
    }
}

// Helper struct for grouping segments
struct SegmentGroup {
    let segments: [TextSegment]
    let isDisplayMath: Bool
    let content: String // Only used for display math
}

// MARK: - Flexible Inline View for Mixed Text and Math
struct FlexibleInlineView: View {
    let segments: [TextSegment]
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        // Use a wrapping layout that handles multi-line content gracefully
        WrappingTextWithMathView(segments: segments, fontSize: fontSize)
    }
}

// MARK: - Wrapping Text with Math View
struct WrappingTextWithMathView: View {
    let segments: [TextSegment]
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        if segments.count == 1 {
            // Single segment - handle directly
            let segment = segments[0]
            if segment.isLaTeX {
                SwiftMathView(
                    latex: segment.content,
                    fontSize: fontSize,
                    displayMode: false
                )
                .frame(height: fontSize * 1.2)
                .alignmentGuide(.bottom) { d in d[.bottom] }
                .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                Text(segment.content)
                    .font(.system(size: fontSize))
                    .foregroundColor(colorScheme == .dark ? .white : .black)
                    .alignmentGuide(.bottom) { d in d[.bottom] }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .fixedSize(horizontal: false, vertical: true)
            }
        } else {
            // Multiple segments - use FlowLayout for wrapping with minimal spacing
            FlowLayout(spacing: 1) {
                ForEach(Array(segments.enumerated()), id: \.offset) { index, segment in
                    if segment.isLaTeX {
                        SwiftMathView(
                            latex: segment.content,
                            fontSize: fontSize,
                            displayMode: false
                        )
                        .frame(height: fontSize * 1.2)
                        .alignmentGuide(.bottom) { d in d[.bottom] }
                        .fixedSize()
                    } else {
                        Text(segment.content)
                            .font(.system(size: fontSize))
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                            .alignmentGuide(.bottom) { d in d[.bottom] }
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

// MARK: - Wrapping HStack for Inline Math
struct WrappingHStack: View {
    let segments: [TextSegment]
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        ViewThatFits(in: .horizontal) {
            // First try: single line layout with minimal spacing
            HStack(alignment: .firstTextBaseline, spacing: 1) {
                ForEach(Array(segments.enumerated()), id: \.offset) { index, segment in
                    if segment.isLaTeX {
                        SwiftMathView(
                            latex: segment.content,
                            fontSize: fontSize,
                            displayMode: false
                        )
                        .fixedSize()
                    } else {
                        Text(segment.content)
                            .font(.system(size: fontSize))
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                            .fixedSize(horizontal: true, vertical: false)
                    }
                }
            }

            // Fallback: vertical layout if horizontal doesn't fit
            VStack(alignment: .leading, spacing: 0) {
                ForEach(Array(segments.enumerated()), id: \.offset) { index, segment in
                    if segment.isLaTeX {
                        SwiftMathView(
                            latex: segment.content,
                            fontSize: fontSize,
                            displayMode: false
                        )
                        .frame(maxWidth: .infinity, alignment: .leading)
                    } else {
                        Text(segment.content)
                            .font(.system(size: fontSize))
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
            }
        }
    }
}

// MARK: - SwiftMath View Wrapper
struct SwiftMathView: UIViewRepresentable {
    let latex: String
    let fontSize: CGFloat
    let displayMode: Bool
    @Environment(\.colorScheme) private var colorScheme

    func makeUIView(context: Context) -> MTMathUILabel {
        let mathView = MTMathUILabel()
        mathView.textAlignment = .left  // Always use left alignment for consistency
        mathView.labelMode = displayMode ? .display : .text

        // For inline math, optimize for inline flow
        if !displayMode {
            mathView.setContentHuggingPriority(.defaultHigh, for: .horizontal)
            mathView.setContentHuggingPriority(.defaultHigh, for: .vertical)
            mathView.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
            mathView.setContentCompressionResistancePriority(.defaultHigh, for: .vertical)
        }

        return mathView
    }

    func updateUIView(_ mathView: MTMathUILabel, context: Context) {
        // Clean and prepare LaTeX
        let cleanedLatex = cleanLaTeXForSwiftMath(latex)
        print("🔧 SwiftMath: Original LaTeX: '\(latex)'")
        print("🔧 SwiftMath: Cleaned LaTeX: '\(cleanedLatex)'")

        // Don't render empty or whitespace-only content
        if cleanedLatex.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("🔧 SwiftMath: Skipping empty content")
            mathView.latex = ""
            return
        }

        mathView.latex = cleanedLatex
        mathView.fontSize = fontSize
        mathView.textColor = colorScheme == .dark ? UIColor.white : UIColor.black

        // Set font if available
        if let font = MTFontManager().latinModernFont(withSize: fontSize) {
            mathView.font = font
        }

        // Force layout update for proper sizing
        mathView.invalidateIntrinsicContentSize()
        mathView.sizeToFit()

        // Debug: Check if the math view has content
        print("🔧 SwiftMath: Math view bounds: \(mathView.bounds)")
        print("🔧 SwiftMath: Math view intrinsic size: \(mathView.intrinsicContentSize)")
    }

    // Clean LaTeX for SwiftMath compatibility
    private func cleanLaTeXForSwiftMath(_ latex: String) -> String {
        var cleaned = latex

        // Remove any surrounding dollar signs if present
        cleaned = cleaned.trimmingCharacters(in: CharacterSet(charactersIn: "$"))

        // First, fix double backslashes in LaTeX commands to single backslashes
        cleaned = fixDoubleBackslashesInLaTeX(cleaned)

        // Handle common LaTeX commands that might need adjustment for SwiftMath
        let replacements: [(String, String)] = [
            ("\\text{", "\\mathrm{"),  // SwiftMath prefers \mathrm over \text
            ("\\cdot", "\\times"),     // Use times for multiplication
            ("^\\circ", "^\\circ"),    // Keep degree symbol as is
            // Try keeping \angle as is for now
        ]

        for (old, new) in replacements {
            cleaned = cleaned.replacingOccurrences(of: old, with: new)
        }

        return cleaned
    }
}

// MARK: - Text Segment Model
struct TextSegment {
    let content: String
    let isLaTeX: Bool
    let isDisplayMode: Bool
}

// MARK: - Flow Layout for Inline Math
struct FlowLayout: Layout {
    let spacing: CGFloat

    init(spacing: CGFloat = 8) {
        self.spacing = spacing
    }

    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let result = FlowResult(
            in: proposal.replacingUnspecifiedDimensions().width,
            subviews: subviews,
            spacing: spacing
        )
        return result.bounds
    }

    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let result = FlowResult(
            in: bounds.width,
            subviews: subviews,
            spacing: spacing
        )
        for (index, subview) in subviews.enumerated() {
            subview.place(at: CGPoint(x: bounds.minX + result.frames[index].minX,
                                    y: bounds.minY + result.frames[index].minY),
                         proposal: ProposedViewSize(result.frames[index].size))
        }
    }
}

struct FlowResult {
    var bounds = CGSize.zero
    var frames: [CGRect] = []

    init(in maxWidth: CGFloat, subviews: LayoutSubviews, spacing: CGFloat) {
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        var lineHeight: CGFloat = 0

        for subview in subviews {
            let size = subview.sizeThatFits(.unspecified)

            if currentX + size.width > maxWidth && currentX > 0 {
                // Move to next line
                currentX = 0
                currentY += lineHeight + spacing
                lineHeight = 0
            }

            frames.append(CGRect(x: currentX, y: currentY, width: size.width, height: size.height))
            currentX += size.width + spacing
            lineHeight = max(lineHeight, size.height)
        }

        bounds = CGSize(width: maxWidth, height: currentY + lineHeight)
    }
}

// MARK: - Helper Functions
private func isStructuredQuestion(_ text: String) -> Bool {
        // Check if text contains sub-question patterns like (a), (b), etc.
        return text.contains("(a)") || text.contains("(b)") || text.contains("(c)") || text.contains("(d)")
    }
    
    private func enhancedMathText(_ text: String) -> String {
        var result = text.replacingOccurrences(of: "$", with: "")
        
        // Handle degree symbols first (most specific patterns)
        result = result.replacingOccurrences(of: "°\\circ", with: "°")
        result = result.replacingOccurrences(of: "\\circ°", with: "°")
        result = result.replacingOccurrences(of: "^°\\circ", with: "°")
        result = result.replacingOccurrences(of: "^\\circ°", with: "°")
        result = result.replacingOccurrences(of: "°°", with: "°")
        result = result.replacingOccurrences(of: "\\circ", with: "°")
        
        // Convert mathematical symbols to Unicode
        result = result.replacingOccurrences(of: "\\times", with: "×")
        result = result.replacingOccurrences(of: "\\div", with: "÷")
        result = result.replacingOccurrences(of: "\\pm", with: "±")
        result = result.replacingOccurrences(of: "\\cdot", with: "·")
        result = result.replacingOccurrences(of: "\\angle", with: "∠")
        result = result.replacingOccurrences(of: "\\leq", with: "≤")
        result = result.replacingOccurrences(of: "\\geq", with: "≥")
        result = result.replacingOccurrences(of: "\\neq", with: "≠")
        result = result.replacingOccurrences(of: "\\approx", with: "≈")
        
        // Greek letters
        result = result.replacingOccurrences(of: "\\pi", with: "π")
        result = result.replacingOccurrences(of: "\\alpha", with: "α")
        result = result.replacingOccurrences(of: "\\beta", with: "β")
        result = result.replacingOccurrences(of: "\\theta", with: "θ")
        result = result.replacingOccurrences(of: "\\lambda", with: "λ")
        result = result.replacingOccurrences(of: "\\sigma", with: "σ")
        
        // Superscripts
        result = result.replacingOccurrences(of: "^2", with: "²")
        result = result.replacingOccurrences(of: "^3", with: "³")
        result = result.replacingOccurrences(of: "^4", with: "⁴")
        result = result.replacingOccurrences(of: "^n", with: "ⁿ")
        
        // Clean up any remaining problematic characters that might cause display issues
        result = result.replacingOccurrences(of: "^°", with: "°")
        
        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }


// MARK: - Structured Question View for Multi-Part Questions
struct StructuredQuestionView: View {
    let text: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        let questionParts = parseStructuredQuestion(text)
        
        VStack(alignment: .leading, spacing: 8) {
            // Display main question/equation first
            if !questionParts.mainContent.isEmpty {
                if containsLaTeX(questionParts.mainContent) {
                    SwiftMathQuestionView(text: questionParts.mainContent, fontSize: fontSize)
                } else {
                    Text(questionParts.mainContent)
                        .font(.system(size: fontSize))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }

            // Display sub-questions with proper formatting
            ForEach(Array(questionParts.subQuestions.enumerated()), id: \.offset) { index, subQuestion in
                HStack(alignment: .top, spacing: 8) {
                    Text(subQuestion.label)
                        .font(.system(size: fontSize, weight: .medium))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .frame(width: 20, alignment: .leading)
                        .fixedSize(horizontal: true, vertical: false)

                    if containsLaTeX(subQuestion.content) {
                        SwiftMathQuestionView(text: subQuestion.content, fontSize: fontSize)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    } else {
                        Text(subQuestion.content)
                            .font(.system(size: fontSize))
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
                .padding(.leading, 0)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // Check if text contains LaTeX expressions
    private func containsLaTeX(_ text: String) -> Bool {
        return text.contains("$") ||
               text.contains("\\frac") ||
               text.contains("\\sqrt") ||
               text.contains("\\times") ||
               text.contains("\\div") ||
               text.contains("\\alpha") ||
               text.contains("\\beta") ||
               text.contains("\\gamma") ||
               text.contains("\\theta") ||
               text.contains("\\pi") ||
               text.contains("\\sum") ||
               text.contains("\\int") ||
               text.contains("\\leq") ||
               text.contains("\\geq") ||
               text.contains("\\neq") ||
               text.contains("\\angle") ||
               text.contains("\\circ") ||
               text.contains("\\perp") ||
               text.contains("\\parallel")
    }
    
    private func parseStructuredQuestion(_ text: String) -> StructuredQuestionContent {
        let subQuestionPattern = #"\([a-d]\)"#
        
        // Find all sub-question markers
        let regex = try! NSRegularExpression(pattern: subQuestionPattern, options: [])
        let matches = regex.matches(in: text, options: [], range: NSRange(text.startIndex..., in: text))
        
        var mainContent = ""
        var subQuestions: [SubQuestion] = []
        
        if matches.isEmpty {
            // No sub-questions found, treat as main content
            mainContent = text
        } else {
            // Extract main content (everything before first sub-question)
            let firstMatch = matches[0]
            let firstMatchStart = text.index(text.startIndex, offsetBy: firstMatch.range.location)
            mainContent = String(text[text.startIndex..<firstMatchStart]).trimmingCharacters(in: .whitespacesAndNewlines)
            
            // Extract sub-questions
            for (index, match) in matches.enumerated() {
                let matchStart = text.index(text.startIndex, offsetBy: match.range.location)
                let matchEnd = text.index(text.startIndex, offsetBy: match.range.location + match.range.length)
                
                let label = String(text[matchStart..<matchEnd])
                
                // Content starts after the label
                let contentStart = matchEnd
                let contentEnd: String.Index
                
                if index < matches.count - 1 {
                    // Not the last sub-question, end at next sub-question
                    let nextMatch = matches[index + 1]
                    contentEnd = text.index(text.startIndex, offsetBy: nextMatch.range.location)
                } else {
                    // Last sub-question, go to end of text
                    contentEnd = text.endIndex
                }
                
                let content = String(text[contentStart..<contentEnd]).trimmingCharacters(in: .whitespacesAndNewlines)
                subQuestions.append(SubQuestion(label: label, content: content))
            }
        }
        
        return StructuredQuestionContent(mainContent: mainContent, subQuestions: subQuestions)
    }
    
    private func enhancedMathText(_ text: String) -> String {
        var result = text.replacingOccurrences(of: "$", with: "")
        
        // Handle degree symbols first (most specific patterns)
        result = result.replacingOccurrences(of: "°\\circ", with: "°")
        result = result.replacingOccurrences(of: "\\circ°", with: "°")
        result = result.replacingOccurrences(of: "^°\\circ", with: "°")
        result = result.replacingOccurrences(of: "^\\circ°", with: "°")
        result = result.replacingOccurrences(of: "°°", with: "°")
        result = result.replacingOccurrences(of: "\\circ", with: "°")
        
        // Convert mathematical symbols to Unicode
        result = result.replacingOccurrences(of: "\\times", with: "×")
        result = result.replacingOccurrences(of: "\\div", with: "÷")
        result = result.replacingOccurrences(of: "\\pm", with: "±")
        result = result.replacingOccurrences(of: "\\cdot", with: "·")
        result = result.replacingOccurrences(of: "\\angle", with: "∠")
        result = result.replacingOccurrences(of: "\\leq", with: "≤")
        result = result.replacingOccurrences(of: "\\geq", with: "≥")
        result = result.replacingOccurrences(of: "\\neq", with: "≠")
        result = result.replacingOccurrences(of: "\\approx", with: "≈")
        
        // Greek letters
        result = result.replacingOccurrences(of: "\\pi", with: "π")
        result = result.replacingOccurrences(of: "\\alpha", with: "α")
        result = result.replacingOccurrences(of: "\\beta", with: "β")
        result = result.replacingOccurrences(of: "\\theta", with: "θ")
        result = result.replacingOccurrences(of: "\\lambda", with: "λ")
        result = result.replacingOccurrences(of: "\\sigma", with: "σ")
        
        // Superscripts
        result = result.replacingOccurrences(of: "^2", with: "²")
        result = result.replacingOccurrences(of: "^3", with: "³")
        result = result.replacingOccurrences(of: "^4", with: "⁴")
        result = result.replacingOccurrences(of: "^n", with: "ⁿ")
        
        // Clean up any remaining problematic characters that might cause display issues
        result = result.replacingOccurrences(of: "^^", with: "°")
        result = result.replacingOccurrences(of: "^°", with: "°")
        
        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - Supporting Types for Structured Questions
struct StructuredQuestionContent {
    let mainContent: String
    let subQuestions: [SubQuestion]
}

struct SubQuestion {
    let label: String
    let content: String
}

// MARK: - Fraction Math View
struct FractionMathView: View {
    let text: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        // Parse and display fractions properly
        if let fractionComponents = parseFractions(text) {
            HStack(spacing: 2) {
                ForEach(Array(fractionComponents.enumerated()), id: \.offset) { index, component in
                    if component.isFraction {
                        FractionView(
                            numerator: component.numerator ?? "",
                            denominator: component.denominator ?? "",
                            fontSize: fontSize
                        )
                    } else {
                        Text(component.text)
                            .font(.system(size: fontSize))
                            .foregroundColor(colorScheme == .dark ? .white : .black)
                    }
                }
            }
        } else {
            Text(text.replacingOccurrences(of: "$", with: ""))
                .font(.system(size: fontSize))
                .foregroundColor(colorScheme == .dark ? .white : .black)
        }
    }
    
    private func parseFractions(_ text: String) -> [MathTextComponent]? {
        var components: [MathTextComponent] = []
        var remainingText = text.replacingOccurrences(of: "$", with: "")
        
        let fractionPattern = #"\\frac\{([^}]+)\}\{([^}]+)\}"#
        
        while let range = remainingText.range(of: fractionPattern, options: .regularExpression) {
            // Add text before fraction
            if range.lowerBound > remainingText.startIndex {
                let beforeText = String(remainingText[remainingText.startIndex..<range.lowerBound])
                if !beforeText.isEmpty {
                    components.append(MathTextComponent(text: beforeText, isFraction: false))
                }
            }
            
            // Extract and add fraction
            let fractionText = String(remainingText[range])
            if let regex = try? NSRegularExpression(pattern: fractionPattern, options: []),
               let match = regex.firstMatch(in: fractionText, options: [], range: NSRange(fractionText.startIndex..., in: fractionText)) {
                let numRange = Range(match.range(at: 1), in: fractionText)!
                let denRange = Range(match.range(at: 2), in: fractionText)!
                let numerator = String(fractionText[numRange])
                let denominator = String(fractionText[denRange])
                
                                 components.append(MathTextComponent(
                     text: "",
                     isFraction: true,
                     numerator: cleanSymbols(numerator),
                     denominator: cleanSymbols(denominator)
                 ))
            }
            
            remainingText.removeSubrange(remainingText.startIndex..<range.upperBound)
        }
        
        // Add remaining text
        if !remainingText.isEmpty {
            components.append(MathTextComponent(text: remainingText, isFraction: false))
        }
        
        return components.isEmpty ? nil : components
    }
    
    private func cleanSymbols(_ text: String) -> String {
        var result = text
        
        // Handle degree symbols first (most specific patterns)
        result = result.replacingOccurrences(of: "°\\circ", with: "°")
        result = result.replacingOccurrences(of: "\\circ°", with: "°")
        result = result.replacingOccurrences(of: "^°\\circ", with: "°")
        result = result.replacingOccurrences(of: "^\\circ°", with: "°")
        result = result.replacingOccurrences(of: "°°", with: "°")
        result = result.replacingOccurrences(of: "\\circ", with: "°")
        
        // Convert LaTeX symbols to Unicode
        result = result.replacingOccurrences(of: "\\times", with: "×")
        result = result.replacingOccurrences(of: "\\div", with: "÷")
        result = result.replacingOccurrences(of: "\\pm", with: "±")
        result = result.replacingOccurrences(of: "\\cdot", with: "·")
        result = result.replacingOccurrences(of: "\\angle", with: "∠")
        result = result.replacingOccurrences(of: "\\leq", with: "≤")
        result = result.replacingOccurrences(of: "\\geq", with: "≥")
        result = result.replacingOccurrences(of: "\\neq", with: "≠")
        result = result.replacingOccurrences(of: "\\approx", with: "≈")
        
        // Greek letters
        result = result.replacingOccurrences(of: "\\pi", with: "π")
        result = result.replacingOccurrences(of: "\\alpha", with: "α")
        result = result.replacingOccurrences(of: "\\beta", with: "β")
        result = result.replacingOccurrences(of: "\\theta", with: "θ")
        result = result.replacingOccurrences(of: "\\lambda", with: "λ")
        result = result.replacingOccurrences(of: "\\sigma", with: "σ")
        
        // Superscripts
        result = result.replacingOccurrences(of: "^2", with: "²")
        result = result.replacingOccurrences(of: "^3", with: "³")
        result = result.replacingOccurrences(of: "^4", with: "⁴")
        result = result.replacingOccurrences(of: "^n", with: "ⁿ")
        
        // Clean up any remaining problematic characters
        result = result.replacingOccurrences(of: "^^", with: "°")
        result = result.replacingOccurrences(of: "^°", with: "°")
        
        return result
    }
}

// MARK: - Supporting Types and Views
struct MathTextComponent {
    let text: String
    let isFraction: Bool
    let numerator: String?
    let denominator: String?
    
    init(text: String, isFraction: Bool, numerator: String? = nil, denominator: String? = nil) {
        self.text = text
        self.isFraction = isFraction
        self.numerator = numerator
        self.denominator = denominator
    }
}

struct FractionView: View {
    let numerator: String
    let denominator: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(spacing: 1) {
            Text(numerator)
                .font(.system(size: fontSize * 1))
                .foregroundColor(colorScheme == .dark ? .white : .black)
            
            Rectangle()
                .fill(colorScheme == .dark ? Color.white : Color.black)
                .frame(height: 1.5)
                .frame(width: max(
                    estimateTextWidth(numerator, fontSize: fontSize * 1),
                    estimateTextWidth(denominator, fontSize: fontSize * 1)
                ) + 8) // Add small padding
            
            Text(denominator)
                .font(.system(size: fontSize * 1))
                .foregroundColor(colorScheme == .dark ? .white : .black)
        }
    }
    
    private func estimateTextWidth(_ text: String, fontSize: CGFloat) -> CGFloat {
        // Rough estimation of text width - adjust multiplier as needed
        return CGFloat(text.count) * fontSize * 0.45
    }
}
