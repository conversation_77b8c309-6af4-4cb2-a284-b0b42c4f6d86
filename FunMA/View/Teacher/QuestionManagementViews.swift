import SwiftUI

// MARK: - AI Question Generator
class AIQuestionGenerator {
    static let shared = AIQuestionGenerator()

    private init() {}

    func generateQuestions(
        topic: String,
        subtopic: String?,
        numberOfQuestions: Int,
        difficulty: String,
        questionTypes: [QuestionType],
        customPrompt: String? = nil
    ) async throws -> [Question] {
        print("🤖 AI Generation: Starting question generation")
        print("🤖 AI Generation: Topic: \(topic)")
        print("🤖 AI Generation: Subtopic: \(subtopic ?? "None")")
        print("🤖 AI Generation: Number of questions: \(numberOfQuestions)")
        print("🤖 AI Generation: Difficulty: \(difficulty)")

        // Prepare request payload
        var payload: [String: Any] = [
            "topic": topic,
            "difficulty": difficulty,
            "num_questions": numberOfQuestions,
            "deduct_credits": false // Set to false for testing
        ]

        // Add optional fields
        if let subtopic = subtopic, !subtopic.isEmpty {
            payload["subtopic"] = subtopic
        }

        if let customPrompt = customPrompt, !customPrompt.isEmpty {
            payload["custom_prompt"] = customPrompt
        }

        print("🤖 AI Generation: Request payload: \(payload)")

        // Make API request
        guard let url = URL(string: APIConfig.llmGenerateMCQuestionsEndpoint) else {
            throw NSError(domain: "AIQuestionGenerator", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Invalid API URL"
            ])
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authentication headers
        if let authHeaders = UserManager.shared.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                request.addValue(value, forHTTPHeaderField: key)
            }
        }

        // Encode request body
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: payload)
            request.httpBody = jsonData
        } catch {
            throw NSError(domain: "AIQuestionGenerator", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Failed to encode request: \(error.localizedDescription)"
            ])
        }

        // Send request
        let (data, response) = try await URLSession.shared.data(for: request)

        // Check HTTP response
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Invalid response"
            ])
        }

        print("🤖 AI Generation: HTTP Status: \(httpResponse.statusCode)")

        if httpResponse.statusCode != 200 {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("❌ AI Generation: Error response: \(errorMessage)")
            throw NSError(domain: "AIQuestionGenerator", code: httpResponse.statusCode, userInfo: [
                NSLocalizedDescriptionKey: "API Error (\(httpResponse.statusCode)): \(errorMessage)"
            ])
        }

        // Parse response
        return try parseQuestionsResponse(data: data)
    }

    private func parseQuestionsResponse(data: Data) throws -> [Question] {
        print("🔍 AI Generation: Parsing response data")

        // Convert data to string for debugging
        guard let responseString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Unable to decode response as UTF-8"
            ])
        }
        
        print("🔍 AI Generation: Raw response: \(responseString.prefix(500))...")

        // Parse outer JSON response
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Invalid JSON response"
            ])
        }

        print("🔍 AI Generation: Outer response structure: \(json.keys)")

        // Extract the textResponse containing the questions JSON
        guard let generatedContent = json["generated_content"] as? [String: Any],
              let textResponse = generatedContent["textResponse"] as? String else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Missing textResponse in generated_content"
            ])
        }

        // Use the new robust parsing method
        return try parseQuestionsFromTextResponse(textResponse)
    }
    
    private func parseQuestionsFromTextResponse(_ textResponse: String) throws -> [Question] {
        print("🔍 AI Generation: Starting robust text response parsing")
        
        // Step 1: Extract raw JSON from markdown
        let rawJSON = extractJSONFromMarkdown(textResponse)
        print("🔍 AI Generation: Extracted raw JSON length: \(rawJSON.count)")
        
        // Step 2: Use a completely different approach - manual JSON-like parsing
        // Instead of fixing escapes, we'll extract the data we need directly
        let questions = try parseQuestionsManually(from: rawJSON)
        
        print("✅ AI Generation: Successfully parsed \(questions.count) questions manually")
        return questions
    }
    
    private func parseQuestionsManually(from jsonString: String) throws -> [Question] {
        print("🔍 AI Generation: Starting manual JSON parsing")
        
        var questions: [Question] = []
        
        // Find all question objects in the JSON string - updated pattern to include explanation and step-by-step-solution
        let questionPattern = #"\{\s*"questionText":\s*"([^"]*(?:\\.[^"]*)*)"[\s\S]*?"questionType":\s*"([^"]+)"[\s\S]*?"options":\s*\[([\s\S]*?)\][\s\S]*?"correctAnswer":\s*(\d+)[\s\S]*?"explanation":\s*"([^"]*(?:\\.[^"]*)*)"[\s\S]*?"step-by-step-solution":\s*"([^"]*(?:\\.[^"]*)*)"[\s\S]*?"points":\s*(\d+)[\s\S]*?\}"#
        
        do {
            let regex = try NSRegularExpression(pattern: questionPattern, options: [.dotMatchesLineSeparators])
            let matches = regex.matches(in: jsonString, options: [], range: NSRange(location: 0, length: jsonString.utf16.count))
            
            print("🔍 AI Generation: Found \(matches.count) question matches")
            
            for (index, match) in matches.enumerated() {
                do {
                    let question = try parseQuestionFromMatch(match, in: jsonString, index: index)
                    questions.append(question)
                } catch {
                    print("❌ AI Generation: Failed to parse question \(index): \(error)")
                    // Continue with other questions
                }
            }
            
        } catch {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Regex pattern failed: \(error.localizedDescription)"
            ])
        }
        
        // Fallback: If manual parsing fails, try a simpler approach
        if questions.isEmpty {
            print("🔍 AI Generation: Manual parsing failed, trying fallback approach")
            questions = try parseQuestionsWithFallback(from: jsonString)
        }
        
        return questions
    }
    
    private func parseQuestionFromMatch(_ match: NSTextCheckingResult, in text: String, index: Int) throws -> Question {
        let nsString = text as NSString

        // Extract question text
        let questionTextRange = Range(match.range(at: 1), in: text)!
        let questionText = String(text[questionTextRange])
        print("🔍 AI Generation: Raw extracted question text: '\(questionText)'")

        // Extract options
        let optionsRange = Range(match.range(at: 3), in: text)!
        let optionsString = String(text[optionsRange])
        let options = parseOptionsFromString(optionsString)

        // Extract correct answer
        let correctAnswerRange = Range(match.range(at: 4), in: text)!
        let correctAnswerString = String(text[correctAnswerRange])
        let correctAnswerIndex = Int(correctAnswerString) ?? 0

        // Extract explanation
        let explanationRange = Range(match.range(at: 5), in: text)!
        let explanation = String(text[explanationRange])
        
        // Extract step-by-step solution
        let stepByStepRange = Range(match.range(at: 6), in: text)!
        let stepByStepSolution = String(text[stepByStepRange])

        // Extract points
        let pointsRange = Range(match.range(at: 7), in: text)!
        let pointsString = String(text[pointsRange])
        let points = Int(pointsString) ?? 1

        // Clean up the extracted text
        let cleanQuestionText = cleanExtractedText(questionText)
        print("🔍 AI Generation: Cleaned question text: '\(cleanQuestionText)'")
        let cleanOptions = options.map { cleanExtractedText($0) }
        let cleanExplanation = cleanExtractedText(explanation)
        let cleanStepByStepSolution = cleanExtractedText(stepByStepSolution)
        
        print("🔍 AI Generation: Extracted explanation: '\(cleanExplanation)'")
        print("🔍 AI Generation: Extracted step-by-step: '\(cleanStepByStepSolution)'")
        
        return Question(
            questionText: cleanQuestionText,
            questionType: .multipleChoice,
            options: cleanOptions,
            correctAnswerIndex: correctAnswerIndex,
            points: points,
            explanation: cleanExplanation.isEmpty ? nil : cleanExplanation,
            stepByStepSolution: cleanStepByStepSolution.isEmpty ? nil : cleanStepByStepSolution
        )
    }
    
    private func parseOptionsFromString(_ optionsString: String) -> [String] {
        // Simple pattern to extract quoted strings
        let optionPattern = #""([^"\\]*(?:\\.[^"\\]*)*)""#
        
        do {
            let regex = try NSRegularExpression(pattern: optionPattern, options: [])
            let matches = regex.matches(in: optionsString, options: [], range: NSRange(location: 0, length: optionsString.utf16.count))
            
            return matches.compactMap { match in
                guard let range = Range(match.range(at: 1), in: optionsString) else { return nil }
                return String(optionsString[range])
            }
        } catch {
            print("❌ AI Generation: Failed to parse options: \(error)")
            return []
        }
    }
    
    private func cleanExtractedText(_ text: String) -> String {
        print("🔍 AI Generation: cleanExtractedText input: '\(text)'")
        var cleaned = text

        // Handle JSON escape sequences, but preserve LaTeX commands
        let beforeQuotes = cleaned
        cleaned = cleaned.replacingOccurrences(of: "\\\"", with: "\"")
        if beforeQuotes != cleaned {
            print("🔍 AI Generation: After quote unescaping: '\(cleaned)'")
        }

        // Only convert non-LaTeX double backslashes to single backslashes
        // Preserve LaTeX commands like \\times, \\frac, etc.
        let beforeBackslash = cleaned
        do {
            let regex = try NSRegularExpression(pattern: #"\\\\(?![a-zA-Z])"#, options: [])
            cleaned = regex.stringByReplacingMatches(
                in: cleaned,
                options: [],
                range: NSRange(cleaned.startIndex..., in: cleaned),
                withTemplate: "\\\\"
            )
        } catch {
            // Fallback: only convert \\\\ to \\ if not followed by a letter
            print("Warning: Could not compile regex for JSON unescaping, using simple replacement")
        }
        if beforeBackslash != cleaned {
            print("🔍 AI Generation: After backslash normalization: '\(cleaned)'")
        }

        let beforeNewlines = cleaned
        // FIXED: Be very specific about escape sequence conversion to avoid breaking LaTeX
        // Only convert escape sequences that are clearly JSON escapes, not LaTeX commands

        // Use regex to only convert escape sequences that are NOT followed by 'imes' (from \times)
        do {
            // Convert \n to newline, but not if it's part of a LaTeX command
            let newlineRegex = try NSRegularExpression(pattern: #"\\n(?![a-zA-Z])"#, options: [])
            cleaned = newlineRegex.stringByReplacingMatches(
                in: cleaned,
                options: [],
                range: NSRange(cleaned.startIndex..., in: cleaned),
                withTemplate: "\n"
            )

            // Convert \r to carriage return, but not if it's part of a LaTeX command
            let carriageRegex = try NSRegularExpression(pattern: #"\\r(?![a-zA-Z])"#, options: [])
            cleaned = carriageRegex.stringByReplacingMatches(
                in: cleaned,
                options: [],
                range: NSRange(cleaned.startIndex..., in: cleaned),
                withTemplate: "\r"
            )

            // CRITICAL: Do NOT convert \t at all, as it's part of \times, \tan, etc.
            // If we really need to handle \t escapes, we'd need a very specific pattern
            // For now, skip \t conversion entirely to preserve LaTeX commands

        } catch {
            print("Warning: Could not compile regex for escape sequence conversion")
        }

        if beforeNewlines != cleaned {
            print("🔍 AI Generation: After escape sequence normalization: '\(cleaned)'")
        }

        // Apply LaTeX normalization
        let result = normalizeLaTeX(cleaned)
        print("🔍 AI Generation: cleanExtractedText output: '\(result)'")
        return result
    }
    
    private func parseQuestionsWithFallback(from jsonString: String) throws -> [Question] {
        print("🔍 AI Generation: Using fallback parsing approach")
        
        // Very simple fallback - just try to find question texts and options
        var questions: [Question] = []
        
        // Look for question patterns
        let simpleQuestionPattern = #""questionText":\s*"([^"]+)""#
        let simpleOptionsPattern = #""options":\s*\[\s*"([^"]+)"\s*,\s*"([^"]+)"\s*,\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\]"#
        
        do {
            let questionRegex = try NSRegularExpression(pattern: simpleQuestionPattern, options: [])
            let questionMatches = questionRegex.matches(in: jsonString, options: [], range: NSRange(location: 0, length: jsonString.utf16.count))
            
            let optionsRegex = try NSRegularExpression(pattern: simpleOptionsPattern, options: [])
            let optionsMatches = optionsRegex.matches(in: jsonString, options: [], range: NSRange(location: 0, length: jsonString.utf16.count))
            
            let count = min(questionMatches.count, optionsMatches.count)
            
            for i in 0..<count {
                let questionMatch = questionMatches[i]
                let optionsMatch = optionsMatches[i]
                
                if let questionRange = Range(questionMatch.range(at: 1), in: jsonString),
                   optionsMatch.numberOfRanges >= 5 {
                    
                    let questionText = String(jsonString[questionRange])
                    
                    var options: [String] = []
                    for j in 1...4 {
                        if let optionRange = Range(optionsMatch.range(at: j), in: jsonString) {
                            options.append(String(jsonString[optionRange]))
                        }
                    }
                    
                    if !options.isEmpty {
                        let question = Question(
                            questionText: cleanExtractedText(questionText),
                            questionType: .multipleChoice,
                            options: options.map { cleanExtractedText($0) },
                            correctAnswerIndex: 0, // Default to first option
                            points: 1
                        )
                        questions.append(question)
                    }
                }
            }
            
        } catch {
            print("❌ AI Generation: Fallback parsing also failed: \(error)")
        }
        
        return questions
    }

    private func parseQuestion(from dict: [String: Any], index: Int) throws -> Question {
        guard let questionText = dict["question"] as? String ?? dict["questionText"] as? String else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Missing question text for question \(index)"
            ])
        }

        // Parse options
        var options: [String] = []
        if let optionsArray = dict["options"] as? [String] {
            options = optionsArray
        } else if let optionsDict = dict["options"] as? [String: String] {
            // Convert dictionary to array (A, B, C, D order)
            let sortedKeys = optionsDict.keys.sorted()
            options = sortedKeys.compactMap { optionsDict[$0] }
        }

        // Parse correct answer - handle both numeric and string formats
        var correctAnswerIndex = 0
        if let correctAnswer = dict["correct_answer"] as? String {
            // If it's a letter (A, B, C, D), convert to index
            if let firstChar = correctAnswer.uppercased().first,
               let index = "ABCD".firstIndex(of: firstChar) {
                correctAnswerIndex = "ABCD".distance(from: "ABCD".startIndex, to: index)
            } else if let index = Int(correctAnswer) {
                correctAnswerIndex = index
            }
        } else if let index = dict["correct_answer_index"] as? Int {
            correctAnswerIndex = index
        } else if let index = dict["correctAnswerIndex"] as? Int {
            correctAnswerIndex = index
        } else if let index = dict["correctAnswer"] as? Int {
            // Handle the new format where correctAnswer is directly an integer
            correctAnswerIndex = index
        }

        // Parse points
        let points = dict["points"] as? Int ?? 1
        
        // Parse explanation and step-by-step solution
        let explanation = dict["explanation"] as? String
        let stepByStepSolution = dict["stepByStepSolution"] as? String ?? dict["step-by-step-solution"] as? String
        
        // Debug logging to see what fields are available
        print("🔍 Question \(index) fields:")
        print("   explanation: \(explanation ?? "nil")")
        print("   stepByStepSolution: \(stepByStepSolution ?? "nil")")
        print("   Available keys: \(dict.keys.sorted())")

        // Normalize LaTeX in question text and options
        let normalizedQuestionText = normalizeLaTeX(questionText)
        let normalizedOptions = options.map { normalizeLaTeX($0) }
        
        // Normalize LaTeX in explanation and solution if they exist
        let normalizedExplanation = explanation.map { normalizeLaTeX($0) }
        let normalizedStepByStepSolution = stepByStepSolution.map { normalizeLaTeX($0) }

        return Question(
            questionText: normalizedQuestionText,
            questionType: .multipleChoice,
            options: normalizedOptions.isEmpty ? nil : normalizedOptions,
            correctAnswerIndex: correctAnswerIndex,
            points: points,
            explanation: normalizedExplanation,
            stepByStepSolution: normalizedStepByStepSolution
        )
    }

    private func extractJSONFromMarkdown(_ text: String) -> String {
        // Check if the text contains markdown code blocks
        if text.contains("```json") && text.contains("```") {
            // Extract content between ```json and the next ```
            // Updated pattern to be more flexible with whitespace and newlines
            let pattern = "```json\\s*(.*?)\\s*```"
            let options: NSRegularExpression.Options = [.dotMatchesLineSeparators]

            if let regex = try? NSRegularExpression(pattern: pattern, options: options),
               let match = regex.firstMatch(in: text, options: [], range: NSRange(location: 0, length: text.utf16.count)) {

                if let range = Range(match.range(at: 1), in: text) {
                    let extracted = String(text[range]).trimmingCharacters(in: .whitespacesAndNewlines)
                    print("🔍 AI Generation: Extracted JSON from markdown: \(extracted.prefix(100))...")

                    return extracted
                }
            }
        }

        // If no markdown code blocks found or extraction failed, return the original text
        print("🔍 AI Generation: No markdown code blocks found, returning original text")
        return text
    }

}

// MARK: - LaTeX Normalization Functions
/// Normalize LaTeX expressions to fix common issues from LLM responses
func normalizeLaTeX(_ text: String) -> String {
    var normalized = text

    // PRIORITY FIX: Handle escaped dollar currency patterns FIRST before other normalization
    // This fixes the issue where $\$10$ gets incorrectly processed
    // Convert $\$10$ -> $10, $\$5$ -> $5, etc.
    let escapedDollarPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#
    normalized = normalized.replacingOccurrences(
        of: escapedDollarPattern,
        with: "$1",
        options: .regularExpression
    )

    // Step 1: Fix double-escaped LaTeX commands from JSON (\\\\times -> \\times)
    // Only process actual double-escaped commands, not single-escaped ones
    let beforeLatexNormalization = normalized

    // Use simple string replacements for common LaTeX commands - more reliable than regex
    let latexCommands = [
        ("\\\\times", "\\times"),
        ("\\\\div", "\\div"),
        ("\\\\frac", "\\frac"),
        ("\\\\sqrt", "\\sqrt"),
        ("\\\\pm", "\\pm"),
        ("\\\\mp", "\\mp"),
        ("\\\\leq", "\\leq"),
        ("\\\\geq", "\\geq"),
        ("\\\\neq", "\\neq"),
        ("\\\\approx", "\\approx"),
        ("\\\\alpha", "\\alpha"),
        ("\\\\beta", "\\beta"),
        ("\\\\gamma", "\\gamma"),
        ("\\\\delta", "\\delta"),
        ("\\\\theta", "\\theta"),
        ("\\\\pi", "\\pi"),
        ("\\\\sin", "\\sin"),
        ("\\\\cos", "\\cos"),
        ("\\\\tan", "\\tan"),
        ("\\\\log", "\\log"),
        ("\\\\ln", "\\ln")
    ]

    for (doubleEscaped, singleEscaped) in latexCommands {
        if normalized.contains(doubleEscaped) {
            normalized = normalized.replacingOccurrences(of: doubleEscaped, with: singleEscaped)
            print("🔧 normalizeLaTeX: Fixed '\(doubleEscaped)' -> '\(singleEscaped)'")
        }
    }

    if beforeLatexNormalization != normalized {
        print("🔧 normalizeLaTeX: LaTeX command normalization: '\(beforeLatexNormalization)' -> '\(normalized)'")
    }

    // Step 2: Remove any stray quotes around LaTeX expressions
    normalized = normalized.replacingOccurrences(of: "\"$", with: "$")
    normalized = normalized.replacingOccurrences(of: "$\"", with: "$")

    // Step 3: Ensure proper spacing around operators
    let beforeSpacing = normalized
    normalized = normalized.replacingOccurrences(of: "\\pm", with: " \\pm ")
    normalized = normalized.replacingOccurrences(of: "\\mp", with: " \\mp ")
    normalized = normalized.replacingOccurrences(of: "\\times", with: " \\times ")
    normalized = normalized.replacingOccurrences(of: "\\div", with: " \\div ")
    if beforeSpacing != normalized {
        print("🔧 normalizeLaTeX: Spacing normalization: '\(beforeSpacing)' -> '\(normalized)'")
    }
    normalized = normalized.replacingOccurrences(of: "\\cdot", with: " \\cdot ")

    // Clean up multiple spaces
    normalized = normalized.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)

    // Step 4: Fix common fraction issues
    normalized = normalized.replacingOccurrences(of: "\\frac{([^}]+)}{([^}]+)}", with: "\\frac{$1}{$2}", options: .regularExpression)

    return normalized
}

struct ManualAddQuestionView: View {
    @Environment(\.dismiss) private var dismiss
    let onAdd: (Question) -> Void
    let editingQuestion: Question? // Optional question for editing mode

    @State private var questionText = ""
    @State private var questionType: QuestionType = .multipleChoice
    @State private var options: [String] = ["", "", "", ""]
    @State private var correctAnswerIndex = 0
    @State private var points = 1
    @State private var showError = false
    @State private var errorMessage = ""

    // Computed property to determine if we're in edit mode
    private var isEditMode: Bool {
        return editingQuestion != nil
    }

    // Initialize with existing question data if editing
    init(onAdd: @escaping (Question) -> Void, editingQuestion: Question? = nil) {
        self.onAdd = onAdd
        self.editingQuestion = editingQuestion

        if let question = editingQuestion {
            self._questionText = State(initialValue: question.questionText)
            self._questionType = State(initialValue: question.questionType)
            self._options = State(initialValue: question.options ?? ["", "", "", ""])
            self._correctAnswerIndex = State(initialValue: question.correctAnswerIndex)
            self._points = State(initialValue: question.points)
        }
    }
    
    private var validOptions: [String] {
        options.filter { !$0.isEmpty }
    }
    
    private var isAddButtonDisabled: Bool {
        if questionText.isEmpty {
            return true
        }
        
        // For multiple choice, need at least 2 options
        if questionType == .multipleChoice {
            return validOptions.count < 2
        }
        
        // For long question, just need question text
        return false
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Question Details") {
                    LaTeXTextEditor(
                        text: $questionText,
                        placeholder: "Question Text",
                        minLines: 3,
                        maxLines: 6
                    )
                    
                    Picker("Question Type", selection: $questionType) {
                        Text("Multiple Choice").tag(QuestionType.multipleChoice)
                        Text("Long Question").tag(QuestionType.longQuestion)
                    }
                    .onChange(of: questionType) { newType in
                        // Reset options when switching to long question
                        if newType == .longQuestion {
                            options = ["", "", "", ""]
                            correctAnswerIndex = 0
                        }
                    }
                }
                
                // Only show options section for multiple choice questions
                if questionType == .multipleChoice {
                    Section("Options") {
                        ForEach(0..<4) { index in
                            HStack(alignment: .bottom, spacing: 8) {
                                Text("\(Character(UnicodeScalar(65 + index)!))")
                                    .font(.system(size: 18))
                                    .foregroundColor(.gray)
                                    .frame(width: 20)
                                    .alignmentGuide(.bottom) { d in d[.bottom] }
                                LaTeXTextEditor(
                                    text: $options[index],
                                    placeholder: "Option \(index + 1)",
                                    minLines: 1,
                                    maxLines: 3
                                )
                                .alignmentGuide(.bottom) { d in d[.bottom] }
                            }
                        }
                    }
                }
                
                // Question Preview Section - Always visible, placed after Options
                Section("Question Preview") {
                    VStack(alignment: .leading, spacing: 16) {
                        if !questionText.isEmpty || validOptions.count >= 2 {
                            VStack(alignment: .leading, spacing: 12) {
                                // Question Text Preview (larger font)
                                if !questionText.isEmpty {
                                    QuestionTextRenderer(text: questionText, fontSize: 20)
                                }
                                
                                // Options Preview (larger font, same container)
                                if validOptions.count >= 2 {
                                    ForEach(Array(validOptions.enumerated()), id: \.offset) { index, option in
                                        HStack(alignment: .top, spacing: 8) {
                                            Text("\(Character(UnicodeScalar(65 + index)!))")
                                                .font(.system(size: 18))
                                                .fontWeight(.medium)
                                                .foregroundColor(.gray)
                                                .frame(width: 20, alignment: .leading)

                                            QuestionTextRenderer(text: option, fontSize: 18)
                                                .frame(maxWidth: .infinity, alignment: .leading)
                                        }
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 12)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            
                            Text("This is how students will see the question with rendered mathematical expressions")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .italic()
                        } else {
                            VStack(spacing: 8) {
                                Image(systemName: "eye")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("Enter question and options above to see preview")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.center)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 20)
                        }
                    }
                }
                
                Section("Answer & Scoring") {
                    // Only show correct answer picker for multiple choice questions
                    if questionType == .multipleChoice {
                        if validOptions.count >= 2 {
                            let options = validOptions
                            Picker("Correct Answer", selection: $correctAnswerIndex) {
                                ForEach(0..<options.count, id: \.self) { index in
                                    HStack {
                                        Text("\(Character(UnicodeScalar(65 + index)!))")
                                        QuestionTextRenderer(text: options[index], fontSize: 16)
                                    }.tag(index)
                                }
                            }
                        } else {
                            Text("Add at least 2 options to select correct answer")
                                .foregroundColor(.gray)
                        }
                    } else {
                        Text("Long questions will be graded manually")
                            .foregroundColor(.gray)
                            .font(.caption)
                    }
                    
                    Stepper("Points: \(points)", value: $points, in: 1...10)
                }
            }
            .navigationTitle(isEditMode ? "Edit Question" : "Add Question")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                },
                trailing: Button(isEditMode ? "Save" : "Add") {
                    validateAndAddQuestion()
                }
                .disabled(isAddButtonDisabled)
            )
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    private func validateAndAddQuestion() {
        // Validate the question
        if questionText.isEmpty {
            errorMessage = "Please enter a question"
            showError = true
            return
        }

        // Validate based on question type
        if questionType == .multipleChoice {
            if validOptions.count < 2 {
                errorMessage = "Please add at least 2 options for multiple choice questions"
                showError = true
                return
            }
        }

        // Create the question (with existing ID if editing)
        let question = Question(
            id: isEditMode ? editingQuestion!.id : UUID(), // Keep existing ID when editing
            questionText: normalizeLaTeX(questionText),
            questionType: questionType,
            options: questionType == .multipleChoice ? validOptions.map { normalizeLaTeX($0) } : nil,
            correctAnswerIndex: questionType == .multipleChoice ? correctAnswerIndex : 0,
            points: points
        )
        onAdd(question)
        dismiss()
    }
}

// MARK: - AI Question Generation View
struct AIQuestionGenerationView: View {
    @Environment(\.dismiss) private var dismiss
    let topic: String
    let subtopic: String
    let onGenerate: ([Question]) -> Void

    @State private var multipleChoiceCount = 5
    @State private var longAnswerCount = 0
    @State private var difficultyLevel: DifficultyLevel = .intermediate
    @State private var customPrompt = ""
    @State private var isGeneratingQuestions = false
    @State private var showError = false
    @State private var errorMessage = ""

    enum DifficultyLevel: String, CaseIterable {
        case beginner = "Beginner"
        case intermediate = "Intermediate"
        case advanced = "Advanced"

        var apiValue: String {
            return self.rawValue.lowercased()
        }
    }

    private var totalQuestions: Int {
        return multipleChoiceCount + longAnswerCount
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Form
                Form {
                    Section("Difficulty Level") {
                        Picker("Difficulty", selection: $difficultyLevel) {
                            ForEach(DifficultyLevel.allCases, id: \.self) { level in
                                Text(level.rawValue).tag(level)
                            }
                        }
                        .pickerStyle(.segmented)
                    }

                    Section("Question Distribution") {
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Text("Multiple Choice Questions:")
                                Spacer()
                                TextField("5", value: $multipleChoiceCount, format: .number)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .frame(width: 60)
                                    .keyboardType(.numberPad)
                                    .onChange(of: multipleChoiceCount) { newValue in
                                        if newValue < 0 {
                                            multipleChoiceCount = 0
                                        } else if newValue > 20 {
                                            multipleChoiceCount = 20
                                        }
                                    }
                            }

                            HStack {
                                Text("Long Answer Questions:")
                                Spacer()
                                HStack(spacing: 8) {
                                    TextField("0", value: $longAnswerCount, format: .number)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 60)
                                        .keyboardType(.numberPad)
                                        .disabled(true)
                                        .opacity(0.5)

                                    Text("Coming Soon")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }
                            }

                            HStack {
                                Text("Total Questions:")
                                Spacer()
                                Text("\(totalQuestions)")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.black)
                                    .frame(minWidth: 40)
                            }
                        }
                    }

                    Section(header: Text("Custom Instructions (Optional)")) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Provide specific requirements or examples for the AI to follow:")
                                .font(.caption)
                                .foregroundColor(.gray)

                            TextEditor(text: $customPrompt)
                                .frame(minHeight: 100)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }

                        Text("Example: 'Include word problems involving real-world scenarios' or 'Focus on algebraic manipulation with fractions'")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .italic()
                    }


                // Generate Button Section
                Section {
                    Button(action: generateAIQuestions) {
                        HStack {
                            if isGeneratingQuestions {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "wand.and.stars")
                                Text("Generate Questions")
                            }
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            isGeneratingQuestions || totalQuestions == 0 ?
                            Color.gray :
                            Color.blue
                        )
                        .cornerRadius(10)
                    }
                    .disabled(isGeneratingQuestions || totalQuestions == 0)

                    if isGeneratingQuestions {
                        Text("Generating questions... This may take a few seconds.")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.top, 8)
                    }

                    if totalQuestions == 0 {
                        Text("Please select at least one question type to generate")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .padding(.top, 8)
                    }
                }
                }
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    HStack(spacing: 8) {
                        Image(systemName: "wand.and.stars")
                            .foregroundColor(.blue)
                        Text("AI Generation")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                }
            }
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                }
            )
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }

    private var canGenerate: Bool {
        totalQuestions > 0
    }

    private func generateAIQuestions() {
        guard canGenerate else { return }

        isGeneratingQuestions = true

        Task {
            do {
                let questions = try await AIQuestionGenerator.shared.generateQuestions(
                    topic: topic,
                    subtopic: subtopic.isEmpty ? nil : subtopic,
                    numberOfQuestions: multipleChoiceCount,
                    difficulty: difficultyLevel.apiValue,
                    questionTypes: [QuestionType.multipleChoice],
                    customPrompt: customPrompt.isEmpty ? nil : customPrompt
                )

                await MainActor.run {
                    self.isGeneratingQuestions = false
                    self.onGenerate(questions)
                    self.dismiss()
                }
            } catch {
                await MainActor.run {
                    self.isGeneratingQuestions = false
                    self.errorMessage = "Failed to generate questions: \(error.localizedDescription)"
                    self.showError = true
                }
            }
        }
    }
}


