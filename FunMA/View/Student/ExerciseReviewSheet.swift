import SwiftUI
import PencilKit
import Foundation

// MARK: - Exercise Results View
struct ExerciseResultsView: View {
    let exercise: Exercise
    let submissions: [QuestionSubmission]
    let submission: StudentSubmission? // Add full submission object
    let onDismiss: () -> Void
    
    @State private var score: Double = 0
    
    private var hasLongQuestions: Bool {
        exercise.questions.contains { $0.questionType == .longQuestion }
    }

    private var isManuallyGraded: Bool {
        // Check if this submission has been manually graded by teacher
        guard let submission = submission else { return false }

        // If backend provides earnedPoints/totalPoints, it's been processed (possibly manually graded)
        if submission.earnedPoints != nil && submission.totalPoints != nil {
            return true
        }

        // Check if any individual questions have manual grading
        return submission.answers.contains { $0.pointsEarned != nil }
    }

    private var gradingStatusText: String {
        if let submission = submission, let gradingStatus = submission.gradingStatus {
            switch gradingStatus.lowercased() {
            case "manual": return "Manually graded by teacher"
            case "auto": return "Automatically graded"
            case "partial": return "Partially graded by teacher"
            default: return "Graded"
            }
        }
        return isManuallyGraded ? "Includes teacher grading" : "Automatically graded"
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Score Circle
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                Circle()
                    .trim(from: 0, to: score / 100)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 20, lineCap: .round))
                    .frame(width: 200, height: 200)
                    .rotationEffect(.degrees(-90))
                
                VStack {
                    Text("\(Int(score))%")
                        .font(.system(size: 50, weight: .bold))
                    Text(hasLongQuestions ? "Partial Score" : "Score")
                        .font(.title3)
                        .foregroundColor(.gray)

                    // Show grading status
                    if isManuallyGraded {
                        HStack(spacing: 4) {
                            Image(systemName: "person.fill.checkmark")
                                .font(.caption)
                                .foregroundColor(.blue)
                            Text(gradingStatusText)
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .padding(.top, 4)
                    }
                }
            }
            
            // Manual grading note
            if hasLongQuestions {
                Text("Some questions require manual grading by your teacher")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // Question Review
            ScrollView {
                VStack(spacing: 20) {
                    ForEach(exercise.questions) { question in
                        QuestionReviewCard(
                            question: question,
                            submission: submissions.first { $0.questionId == question.id }
                        )
                    }
                }
                .padding()
            }
            
            Button("Done") {
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
            .padding()
        }
        .onAppear {
            calculateScore()
        }
    }
    
    private func calculateScore() {
        // PRIORITY 1: Use backend-calculated earnedPoints and totalPoints (includes teacher manual grading)
        if let submission = submission,
           let earnedPoints = submission.earnedPoints,
           let totalPoints = submission.totalPoints,
           totalPoints > 0 {
            score = (Double(earnedPoints) / Double(totalPoints)) * 100
            print("📊 ✅ Using BACKEND earnedPoints/totalPoints: \(earnedPoints)/\(totalPoints) = \(score)%")
            print("📊 ✅ This score includes any teacher manual grading updates")

            // DIAGNOSTIC: Verify backend calculation by checking individual answers
            verifyBackendCalculation(submission: submission, expectedEarned: earnedPoints, expectedTotal: totalPoints)
            return
        }

        // PRIORITY 2: Use backend score field if available
        if let submission = submission,
           let backendScore = submission.score {
            score = backendScore
            print("📊 ⚠️ Using backend score field: \(backendScore)%")
            print("📊 ⚠️ Note: This may not include latest manual grading updates")
            return
        }

        // PRIORITY 3: Calculate using individual question pointsEarned (from manual grading)
        print("📊 ⚠️ Fallback to local calculation using manual grading data")
        var totalPoints = 0.0
        var earnedPoints = 0.0
        var hasManualGrading = false

        for question in exercise.questions {
            totalPoints += Double(question.points)

            if let questionSubmission = submissions.first(where: { $0.questionId == question.id }) {
                // Use manually graded points if available
                if let manualPoints = questionSubmission.pointsEarned {
                    earnedPoints += manualPoints
                    hasManualGrading = true
                    print("📊 Using manual points for question \(question.questionText.prefix(30)): \(manualPoints)")
                } else if question.questionType == .multipleChoice {
                    // For multiple choice, check if answer is correct
                    if let answerIndex = Int(questionSubmission.answer),
                       answerIndex == question.correctAnswerIndex {
                        earnedPoints += Double(question.points)
                        print("📊 Auto-graded MC question \(question.questionText.prefix(30)): \(question.points) points")
                    }
                }
                // For long questions without manual grading, no points are awarded
            }
        }

        if totalPoints > 0 {
            score = (earnedPoints / totalPoints) * 100
            if hasManualGrading {
                print("📊 ✅ Final score includes manual grading: \(score)%")
            } else {
                print("📊 ⚠️ Final score is auto-calculated only: \(score)%")
            }
        } else {
            score = 0
            print("📊 ❌ No points available, score set to 0%")
        }
    }

    // DIAGNOSTIC: Verify if backend calculation matches individual answer data
    private func verifyBackendCalculation(submission: StudentSubmission, expectedEarned: Double, expectedTotal: Double) {
        print("🔍 DIAGNOSTIC: Verifying backend calculation...")

        var calculatedEarned = 0.0
        var calculatedTotal = 0.0
        var correctAnswers = 0
        var totalAnswers = submission.answers.count

        for answer in submission.answers {
            // Find the corresponding question
            if let question = exercise.questions.first(where: { $0.id == answer.questionId }) {
                calculatedTotal += Double(question.points)

                // Check if answer is actually correct based on question data
                let isActuallyCorrect: Bool
                if let answerIndex = Int(answer.answer) {
                    isActuallyCorrect = answerIndex == question.correctAnswerIndex
                } else {
                    isActuallyCorrect = false
                }

                // Use pointsEarned if available, otherwise check correctness
                if let pointsEarned = answer.pointsEarned {
                    calculatedEarned += pointsEarned
                } else if isActuallyCorrect {
                    calculatedEarned += Double(question.points)
                }

                if isActuallyCorrect {
                    correctAnswers += 1
                }

                print("🔍 Question \(question.questionText.prefix(20))...")
                print("   Student answer: \(answer.answer), Correct index: \(question.correctAnswerIndex)")
                print("   Backend isCorrect: \(answer.isCorrect?.description ?? "nil"), Actually correct: \(isActuallyCorrect)")
                print("   Backend pointsEarned: \(answer.pointsEarned?.description ?? "nil"), Question points: \(question.points)")

                // Flag discrepancies
                if let backendIsCorrect = answer.isCorrect, backendIsCorrect != isActuallyCorrect {
                    print("   ⚠️ DISCREPANCY: Backend isCorrect (\(backendIsCorrect)) != Actually correct (\(isActuallyCorrect))")
                }
            }
        }

        print("🔍 SUMMARY:")
        print("   Backend earnedPoints: \(expectedEarned), Calculated: \(calculatedEarned)")
        print("   Backend totalPoints: \(expectedTotal), Calculated: \(calculatedTotal)")
        print("   Correct answers: \(correctAnswers)/\(totalAnswers)")
        print("   Backend percentage: \((expectedEarned/expectedTotal)*100)%, Calculated: \((calculatedEarned/calculatedTotal)*100)%")

        if abs(expectedEarned - calculatedEarned) > 0.1 {
            print("   🚨 ISSUE: Backend earnedPoints doesn't match calculated points!")
        }
        if abs(expectedTotal - calculatedTotal) > 0.1 {
            print("   🚨 ISSUE: Backend totalPoints doesn't match calculated total!")
        }
    }
}

// MARK: - Question Review Card
struct QuestionReviewCard: View {
    let question: Question
    let submission: QuestionSubmission?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            QuestionTextRenderer(text: question.questionText, fontSize: 18)
                .font(.headline)
            
            if question.questionType == .multipleChoice {
                multipleChoiceReview
            } else if question.questionType == .longQuestion {
                longQuestionReview
            } else {
                // Fallback - try to detect based on answer content
                if let answer = submission?.answer {
                    if answer.hasPrefix("DRAWING:") || answer.hasPrefix("PHOTO:") {
                        longQuestionReview
                    } else {
                        multipleChoiceReview
                    }
                } else {
                    multipleChoiceReview
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Multiple Choice Review
    private var multipleChoiceReview: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Show options with indicators
            ForEach(Array((question.options ?? []).enumerated()), id: \.offset) { index, option in
                HStack {
                    Image(systemName: multipleChoiceIcon(for: index))
                        .foregroundColor(multipleChoiceColor(for: index))
                    QuestionTextRenderer(text: option, fontSize: 16)
                    Spacer()
                }
                .padding(.vertical, 4)
            }
            
            Divider()
            
            // Show student's answer and correct answer separately
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Your Answer:")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Spacer()
                    if let studentAnswer = submission?.answer,
                       let answerIndex = Int(studentAnswer),
                       answerIndex < (question.options?.count ?? 0) {
                        QuestionTextRenderer(
                            text: question.options?[answerIndex] ?? "",
                            fontSize: 14
                        )
                        .foregroundColor(answerIndex == question.correctAnswerIndex ? .green : .red)
                    } else {
                        Text("No answer selected")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                    }
                }
                
                HStack {
                    Text("Correct Answer:")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Spacer()
                    if question.correctAnswerIndex < (question.options?.count ?? 0) {
                        QuestionTextRenderer(
                            text: question.options?[question.correctAnswerIndex] ?? "",
                            fontSize: 14
                        )
                        .foregroundColor(.green)
                    } else {
                        Text("Invalid correct answer index")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.red)
                    }
                }
            }
            .padding(.top, 4)
            
            // Show explanation and step-by-step solution for incorrect answers
            if let studentAnswer = submission?.answer,
               let answerIndex = Int(studentAnswer),
               answerIndex != question.correctAnswerIndex {
                explanationAndSolutionView
            }
        }
    }
    
    // MARK: - Explanation and Solution View
    @ViewBuilder
    private var explanationAndSolutionView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Divider()
                .background(Color.red.opacity(0.3))

            // Explanation Section
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.orange)
                    Text("Explanation")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }

                let explanationText = question.explanation ?? "Explanation will be available when questions are created with AI assistance."
                FormattedExplanationView(text: explanationText, fontSize: 14)
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            }

            // Step-by-Step Solution Section
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "list.number")
                        .foregroundColor(.blue)
                    Text("Step-by-Step Solution")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }

                let solutionText = question.stepByStepSolution ?? "Step-by-step solution will be available when questions are created with AI assistance."
                StepByStepSolutionView(text: solutionText, fontSize: 14)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            }
        }
    }
    
    // MARK: - Long Question Review
    private var longQuestionReview: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Your Answer:")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                if let answer = submission?.answer {
                    if answer.hasPrefix("DRAWING:") {
                        drawingAnswerView(base64String: String(answer.dropFirst("DRAWING:".count)))
                    } else if answer.hasPrefix("PHOTO:") {
                        photoAnswerView(base64String: String(answer.dropFirst("PHOTO:".count)))
                    } else if !answer.isEmpty {
                        Text(answer)
                            .font(.body)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                    } else {
                        Text("No answer provided")
                            .font(.subheadline)
                            .italic()
                            .foregroundColor(.orange)
                    }
                } else {
                    Text("No answer provided")
                        .font(.subheadline)
                        .italic()
                        .foregroundColor(.orange)
                }
            }
            
            // For long questions, we typically don't show a "correct answer"
            // since they're usually subjective or require manual grading
            Text("Note: Long questions require manual review by your teacher.")
                .font(.caption)
                .foregroundColor(.gray)
                .italic()
            
            // Show explanation and step-by-step solution for long questions too
            explanationAndSolutionView
        }
    }
    
    // MARK: - Drawing Answer View
    private func drawingAnswerView(base64String: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "pencil.and.outline")
                    .foregroundColor(.blue)
                Text("Drawing Answer")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .fontWeight(.medium)
            }
            
            drawingContentView(base64String: base64String)
        }
    }
    
    // Helper view to handle drawing creation outside ViewBuilder
    @ViewBuilder
    private func drawingContentView(base64String: String) -> some View {
        if base64String.isEmpty {
            errorView(
                color: .orange,
                message: "Empty drawing data"
            )
        } else {
            let drawingResult = createDrawingFromBase64(base64String)
            
            switch drawingResult {
            case .success(let drawing):
                DrawingPreviewView(drawing: drawing)
                    .frame(height: 300)
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            case .failure(.invalidBase64):
                errorView(
                    color: .orange,
                    message: "Could not decode base64 data"
                )
            case .failure(.invalidDrawingData(let error)):
                errorView(
                    color: .red,
                    message: "Error creating PKDrawing: \(error.localizedDescription)"
                )
            }
        }
    }
    
    // Helper function to create PKDrawing outside ViewBuilder
    private func createDrawingFromBase64(_ base64String: String) -> Result<PKDrawing, DrawingError> {
        guard let drawingData = Data(base64Encoded: base64String) else {
            return .failure(.invalidBase64)
        }
        
        do {
            let drawing = try PKDrawing(data: drawingData)
            return .success(drawing)
        } catch {
            return .failure(.invalidDrawingData(error))
        }
    }
    
    // Helper view for error display
    private func errorView(color: Color, message: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(color)
            Text(message)
                .font(.caption)
                .foregroundColor(color)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
    
    // Error types for drawing creation
    private enum DrawingError: Error {
        case invalidBase64
        case invalidDrawingData(Error)
    }
    
    // MARK: - Photo Answer View
    private func photoAnswerView(base64String: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "camera")
                    .foregroundColor(.green)
                Text("Photo Answer")
                    .font(.caption)
                    .foregroundColor(.green)
                    .fontWeight(.medium)
            }
            
            if let imageData = Data(base64Encoded: base64String),
               let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 200)
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("Could not display photo")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .frame(height: 200)
                .frame(maxWidth: .infinity)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }
    
    // MARK: - Multiple Choice Helper Functions
    private func multipleChoiceIcon(for index: Int) -> String {
        if index == question.correctAnswerIndex {
            return "checkmark.circle.fill"
        } else if let submissionAnswer = submission?.answer,
                  let submissionIndex = Int(submissionAnswer),
                  submissionIndex == index {
            return "record.circle.fill"
        } else {
            return "circle"
        }
    }
    
    private func multipleChoiceColor(for index: Int) -> Color {
        if index == question.correctAnswerIndex {
            return .green
        } else if let submissionAnswer = submission?.answer,
                  let submissionIndex = Int(submissionAnswer),
                  submissionIndex == index {
            return .red
        } else {
            return .gray
        }
    }
}

// MARK: - Exercise Review Sheet
struct ExerciseReviewSheet: View {
    let exercise: Exercise
    @ObservedObject var viewModel: ExerciseViewModel
    let onDismiss: () -> Void
    
    @State private var studentSubmission: StudentSubmission?
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        NavigationStack {
            VStack {
                if isLoading {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Loading your submission...")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let errorMessage = errorMessage {
                    VStack(spacing: 20) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)
                        
                        Text("Could not load submission")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(errorMessage)
                            .font(.body)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("Try Again") {
                            loadStudentSubmission()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding()
                } else if let submission = studentSubmission {
                    ExerciseResultsView(
                        exercise: exercise,
                        submissions: submission.answers,
                        submission: submission,
                        onDismiss: onDismiss
                    )
                } else {
                    VStack(spacing: 20) {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        
                        Text("No submission found")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("It looks like you haven't submitted this exercise yet.")
                            .font(.body)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("Close") {
                            onDismiss()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding()
                }
            }
            .navigationTitle(exercise.title)
            .navigationBarTitleDisplayMode(.inline)
        }
        .task {
            loadStudentSubmission()
        }
    }
    
    private func loadStudentSubmission() {
        isLoading = true
        errorMessage = nil

        Task {
            do {
                print("📋 ExerciseReviewSheet: Loading submission for exercise: \(exercise.title)")
                print("📋 ExerciseReviewSheet: Exercise ID: \(exercise.id)")
                print("📋 ExerciseReviewSheet: Student ID: \(UserManager.shared.currentUser.id)")

                // ALWAYS fetch fresh data from API to get latest teacher grading updates
                // This ensures students see the most recent scores after manual grading
                print("📋 ExerciseReviewSheet: Fetching FRESH submission data from API (forceRefresh=true)")
                try await viewModel.getStudentSubmissions(for: exercise.id, forceRefresh: true)

                // Find the submission for this student
                let submission = viewModel.studentSubmissions.first { submission in
                    submission.exerciseId == exercise.id &&
                    submission.studentId == UserManager.shared.currentUser.id
                }

                await MainActor.run {
                    studentSubmission = submission
                    isLoading = false

                    if let submission = submission {
                        print("📋 ExerciseReviewSheet: ✅ Successfully loaded fresh submission")
                        print("📋 ExerciseReviewSheet: Submission has \(submission.answers.count) answers")
                        print("📋 ExerciseReviewSheet: Backend earnedPoints: \(submission.earnedPoints?.description ?? "nil")")
                        print("📋 ExerciseReviewSheet: Backend totalPoints: \(submission.totalPoints?.description ?? "nil")")
                        print("📋 ExerciseReviewSheet: Backend score: \(submission.score?.description ?? "nil")")
                        print("📋 ExerciseReviewSheet: Grading status: \(submission.gradingStatus ?? "nil")")
                    } else {
                        print("📋 ExerciseReviewSheet: ❌ No submission found for this student")
                    }
                }

            } catch {
                print("❌ ExerciseReviewSheet: Failed to load submission: \(error.localizedDescription)")
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isLoading = false
                }
            }
        }
    }
}

// MARK: - Formatted Explanation View
struct FormattedExplanationView: View {
    let text: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        // Process the text to handle proper line breaks and LaTeX rendering
        let processedText = processExplanationText(text)

        VStack(alignment: .leading, spacing: 8) {
            ForEach(Array(processedText.enumerated()), id: \.offset) { index, paragraph in
                if !paragraph.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    QuestionTextRenderer(text: paragraph, fontSize: fontSize)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
        }
    }

    private func processExplanationText(_ text: String) -> [String] {
        // Split by periods followed by space and capital letter, or by explicit line breaks
        let sentences = text.components(separatedBy: .newlines)
            .flatMap { line in
                // Further split sentences that might be run together
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                if trimmedLine.isEmpty {
                    return [String]()
                }

                // Split on ". " followed by capital letter, but preserve LaTeX expressions
                var result = [String]()
                var currentSentence = ""
                var inLatex = false
                var i = trimmedLine.startIndex

                while i < trimmedLine.endIndex {
                    let char = trimmedLine[i]

                    if char == "$" {
                        inLatex.toggle()
                        currentSentence.append(char)
                    } else if !inLatex && char == "." && i < trimmedLine.index(before: trimmedLine.endIndex) {
                        let nextIndex = trimmedLine.index(after: i)
                        if nextIndex < trimmedLine.endIndex && trimmedLine[nextIndex] == " " {
                            let afterSpaceIndex = trimmedLine.index(after: nextIndex)
                            if afterSpaceIndex < trimmedLine.endIndex && trimmedLine[afterSpaceIndex].isUppercase {
                                currentSentence.append(char)
                                result.append(currentSentence.trimmingCharacters(in: .whitespacesAndNewlines))
                                currentSentence = ""
                                i = nextIndex // Skip the space
                            } else {
                                currentSentence.append(char)
                            }
                        } else {
                            currentSentence.append(char)
                        }
                    } else {
                        currentSentence.append(char)
                    }

                    i = trimmedLine.index(after: i)
                }

                if !currentSentence.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    result.append(currentSentence.trimmingCharacters(in: .whitespacesAndNewlines))
                }

                return result
            }

        return sentences.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }
}

// MARK: - Step-by-Step Solution View
struct StepByStepSolutionView: View {
    let text: String
    let fontSize: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        let steps = parseStepByStepSolution(text)

        VStack(alignment: .leading, spacing: 12) {
            ForEach(Array(steps.enumerated()), id: \.offset) { index, step in
                HStack(alignment: .top, spacing: 8) {
                    // Step number
                    Text("\(index + 1).")
                        .font(.system(size: fontSize, weight: .semibold))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .frame(minWidth: 20, alignment: .leading)

                    // Step content with LaTeX rendering
                    QuestionTextRenderer(text: step, fontSize: fontSize)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
        }
    }

    private func parseStepByStepSolution(_ text: String) -> [String] {
        // First, try to detect if the text already has numbered steps
        let lines = text.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        // Check if text already has numbered format (1. 2. 3. etc.)
        let numberedStepPattern = #"^\d+\.\s*"#
        let hasNumberedSteps = lines.contains { line in
            line.range(of: numberedStepPattern, options: .regularExpression) != nil
        }

        if hasNumberedSteps {
            // Parse existing numbered steps
            var steps = [String]()
            var currentStep = ""

            for line in lines {
                if line.range(of: numberedStepPattern, options: .regularExpression) != nil {
                    // Save previous step if exists
                    if !currentStep.isEmpty {
                        steps.append(currentStep.trimmingCharacters(in: .whitespacesAndNewlines))
                    }
                    // Start new step, removing the number prefix
                    currentStep = line.replacingOccurrences(of: numberedStepPattern, with: "", options: .regularExpression)
                } else {
                    // Continue current step
                    if !currentStep.isEmpty {
                        currentStep += " "
                    }
                    currentStep += line
                }
            }

            // Add the last step
            if !currentStep.isEmpty {
                steps.append(currentStep.trimmingCharacters(in: .whitespacesAndNewlines))
            }

            return steps.filter { !$0.isEmpty }
        } else {
            // Split by sentences or logical breaks for non-numbered text
            let sentences = text.components(separatedBy: .newlines)
                .flatMap { $0.components(separatedBy: ". ") }
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
                .map { sentence in
                    // Add period back if it doesn't end with punctuation
                    if !sentence.hasSuffix(".") && !sentence.hasSuffix("!") && !sentence.hasSuffix("?") && !sentence.hasSuffix(":") {
                        return sentence + "."
                    }
                    return sentence
                }

            return sentences
        }
    }
}

// MARK: - Preview
#Preview("Formatted Views Test") {
    VStack(spacing: 20) {
        Text("Explanation Test")
            .font(.headline)

        FormattedExplanationView(
            text: "Substitute the given values for $a$ and $b$ into the expression and follow the order of operations (PEMDAS/BODMAS): Parentheses/Brackets, Exponents/Orders, Multiplication and Division (from left to right), Addition and Subtraction (from left to right). Be careful with squaring negative numbers and signs during multiplication.",
            fontSize: 14
        )
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)

        Divider()

        Text("Step-by-Step Solution Test")
            .font(.headline)

        StepByStepSolutionView(
            text: "1. Substitute $a = -2$ and $b = 3$ into the expression $(a + b)^2 - 2ab$: $(-2 + 3)^2 - 2(-2)(3)$. 2. Solve the operation inside the parentheses first: $(-2 + 3) = 1$. 3. The expression becomes: $(1)^2 - 2(-2)(3)$. 4. Evaluate the exponent: $(1)^2 = 1$. 5. Perform the multiplication: $2(-2)(3) = -12$. 6. The expression becomes: $1 - (-12)$. 7. Simplify the signs: subtracting a negative is equivalent to adding a positive: $1 + 12 = 13$. 8. The value of the expression is 13.",
            fontSize: 14
        )

        Divider()

        Text("LaTeX Rendering Test")
            .font(.headline)

        VStack(alignment: .leading, spacing: 8) {
            Text("Testing specific expressions:")
                .font(.subheadline)
                .foregroundColor(.gray)

            QuestionTextRenderer(text: "$(-2 + 3) = 1$", fontSize: 14)
            QuestionTextRenderer(text: "$2(-2)(3) = -12$", fontSize: 14)
            QuestionTextRenderer(text: "$1 - (-12)$", fontSize: 14)
            QuestionTextRenderer(text: "$1 + 12 = 13$", fontSize: 14)
            QuestionTextRenderer(text: "$3k$", fontSize: 14)
            QuestionTextRenderer(text: "$5pq$", fontSize: 14)

            Text("Mixed text and LaTeX:")
                .font(.subheadline)
                .foregroundColor(.gray)
                .padding(.top)

            QuestionTextRenderer(text: "Substitute the given values for $a$ and $b$ into the expression.", fontSize: 14)
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
    }
    .padding()
}