import Foundation
import os.log

// MARK: - Logging Utility
class ExerciseLogger {
    static let shared = ExerciseLogger()
    private let logger = Logger(subsystem: "com.luminouseducation.funma", category: "ExerciseData")
    
    private init() {}
    
    func log(_ message: String, type: OSLogType = .default, file: String = #file, function: String = #function, line: Int = #line) {
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        let logMessage = "[\(fileName):\(line)] \(function): \(message)"
        
        switch type {
        case .error:
            logger.error("\(logMessage)")
            print("❌ ERROR: \(logMessage)")
        case .fault:
            logger.fault("\(logMessage)")
            print("🚨 FAULT: \(logMessage)")
        case .info:
            logger.info("\(logMessage)")
            print("ℹ️ INFO: \(logMessage)")
        case .debug:
            logger.debug("\(logMessage)")
            print("🔍 DEBUG: \(logMessage)")
        default:
            logger.notice("\(logMessage)")
            print("📝 LOG: \(logMessage)")
        }
    }
    
    func logExerciseData(_ exercise: Exercise, operation: String) {
        log("=== EXERCISE DATA LOG ===", type: .info)
        log("Operation: \(operation)", type: .info)
        log("Exercise ID: \(exercise.id.uuidString)", type: .info)
        log("Title: \(exercise.title)", type: .info)
        log("Topic: \(exercise.topic)", type: .info)
        log("Subtopic: \(exercise.subtopic)", type: .info)
        log("Classroom IDs: \(exercise.classroomIds.joined(separator: ", "))", type: .info)
        log("Created By: \(exercise.createdBy)", type: .info)
        log("Created At: \(exercise.createdAt)", type: .info)
        log("Due Date: \(exercise.dueDate)", type: .info)
        log("Number of Questions: \(exercise.questions.count)", type: .info)
        
        for (index, question) in exercise.questions.enumerated() {
            log("Question \(index + 1):", type: .debug)
            log("  - ID: \(question.id.uuidString)", type: .debug)
            log("  - Type: \(question.questionType.rawValue)", type: .debug)
            log("  - Text: \(question.questionText)", type: .debug)
            log("  - Points: \(question.points)", type: .debug)
            if let options = question.options {
                log("  - Options: \(options.joined(separator: ", "))", type: .debug)
            }
        }
        log("=== END EXERCISE DATA ===", type: .info)
    }
    
    func logSubmissionData(_ submission: StudentSubmission, operation: String) {
        log("=== SUBMISSION DATA LOG ===", type: .info)
        log("Operation: \(operation)", type: .info)
        log("Submission ID: \(submission.id.uuidString)", type: .info)
        log("Exercise ID: \(submission.exerciseId.uuidString)", type: .info)
        log("Student ID: \(submission.studentId)", type: .info)
        log("Start Time: \(submission.startTime)", type: .info)
        log("End Time: \(submission.endTime?.description ?? "Not set")", type: .info)
        log("Score: \(submission.score?.description ?? "Not set")", type: .info)
        log("Number of Answers: \(submission.answers.count)", type: .info)
        
        for (index, answer) in submission.answers.enumerated() {
            log("Answer \(index + 1):", type: .debug)
            log("  - Question ID: \(answer.questionId.uuidString)", type: .debug)
            log("  - Answer: \(answer.answer)", type: .debug)
            log("  - Is Correct: \(answer.isCorrect?.description ?? "Not graded")", type: .debug)
            log("  - Points Earned: \(answer.pointsEarned?.description ?? "Not set")", type: .debug)
        }
        log("=== END SUBMISSION DATA ===", type: .info)
    }
    
    func logNetworkRequest(url: String, method: String, headers: [String: String]? = nil, body: Data? = nil) {
        log("=== NETWORK REQUEST LOG ===", type: .info)
        log("URL: \(url)", type: .info)
        log("Method: \(method)", type: .info)
        if let headers = headers {
            log("Headers: \(headers)", type: .debug)
        }
        if let body = body {
            if let jsonString = String(data: body, encoding: .utf8) {
                log("Request Body: \(jsonString)", type: .debug)
            } else {
                log("Request Body: [Binary data, size: \(body.count) bytes]", type: .debug)
            }
        }
        log("=== END NETWORK REQUEST ===", type: .info)
    }
    
    func logNetworkResponse(statusCode: Int, headers: [String: String]? = nil, data: Data? = nil, error: Error? = nil) {
        log("=== NETWORK RESPONSE LOG ===", type: .info)
        log("Status Code: \(statusCode)", type: .info)
        if let headers = headers {
            log("Response Headers: \(headers)", type: .debug)
        }
        if let error = error {
            log("Response Error: \(error.localizedDescription)", type: .error)
        }
        if let data = data {
            if let jsonString = String(data: data, encoding: .utf8) {
                log("Response Body: \(jsonString)", type: .debug)
            } else {
                log("Response Body: [Binary data, size: \(data.count) bytes]", type: .debug)
            }
        }
        log("=== END NETWORK RESPONSE ===", type: .info)
    }
}

// Enum for question types
enum QuestionType: String, Codable {
    case multipleChoice = "Multiple Choice"
    case longQuestion = "Long Question"
    
    // Custom decoder to handle different backend formats
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        
        switch rawValue.lowercased() {
        case "multiple choice", "multiple_choice", "multiplechoice", "mc":
            self = .multipleChoice
        case "short answer", "short_answer", "shortanswer", "long_question", "long_answer", "longanswer", "open_ended", "text":
            self = .longQuestion
        default:
            // Try the original raw value first
            if let type = QuestionType(rawValue: rawValue) {
                self = type
            } else {
                // Default to long question for unknown types
                self = .longQuestion
            }
        }
    }
    
    // Custom encoder to maintain backend compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(self.rawValue)
    }
    case shortAnswer = "Short Answer"
    case longAnswer = "Long Answer"
    case trueFalse = "True/False"
    case essay = "Essay"
    case fillInBlank = "Fill in the Blank"
}

// Model for a single question
struct Question: Identifiable, Codable {
    let id: UUID
    var questionText: String
    var questionType: QuestionType
    var options: [String]? // For multiple choice questions
    var correctAnswerIndex: Int // Index of correct answer (0-3)
    var points: Int
    var explanation: String? // Explanation for the correct answer
    var stepByStepSolution: String? // Step-by-step solution
    
    init(id: UUID = UUID(), questionText: String, questionType: QuestionType, options: [String]? = nil, correctAnswerIndex: Int, points: Int = 1, explanation: String? = nil, stepByStepSolution: String? = nil) {
        self.id = id
        self.questionText = questionText
        self.questionType = questionType
        self.options = options
        self.correctAnswerIndex = correctAnswerIndex
        self.points = points
        self.explanation = explanation
        self.stepByStepSolution = stepByStepSolution
    }
    
    // Custom decoder to handle backend UUID format and MongoDB extended JSON for Ints
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Decode UUID from string
        let idString = try container.decode(String.self, forKey: .id)
        guard let uuid = UUID(uuidString: idString) else {
            throw DecodingError.dataCorruptedError(
                forKey: .id,
                in: container,
                debugDescription: "Invalid UUID string for question: \(idString)"
            )
        }
        self.id = uuid
        
        // Decode other fields normally
        self.questionText = try container.decode(String.self, forKey: .questionText)
        self.questionType = try container.decode(QuestionType.self, forKey: .questionType)
        self.options = try container.decodeIfPresent([String].self, forKey: .options)
        
        // Decode correctAnswerIndex (plain Int or MongoDB extended JSON)
        self.correctAnswerIndex = try Self.decodeMongoInt(forKey: .correctAnswerIndex, from: container)
        // Decode points (plain Int or MongoDB extended JSON)
        self.points = try Self.decodeMongoInt(forKey: .points, from: container)
        
        // Decode new optional fields
        self.explanation = try container.decodeIfPresent(String.self, forKey: .explanation)
        self.stepByStepSolution = try container.decodeIfPresent(String.self, forKey: .stepByStepSolution)
    }
    
    private static func decodeMongoInt(forKey key: CodingKeys, from container: KeyedDecodingContainer<CodingKeys>) throws -> Int {
        // Try plain Int first
        if let intValue = try? container.decode(Int.self, forKey: key) {
            return intValue
        }
        // Try MongoDB extended JSON
        if let mongoObj = try? container.decode([String: String].self, forKey: key),
           let str = mongoObj["$numberInt"], let intValue = Int(str) {
            return intValue
        }
        throw DecodingError.dataCorruptedError(forKey: key, in: container, debugDescription: "Cannot decode Int or MongoDB extended Int for key \(key)")
    }
    
    // Custom encoder to maintain compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // Encode UUID as string
        try container.encode(id.uuidString, forKey: .id)
        try container.encode(questionText, forKey: .questionText)
        try container.encode(questionType, forKey: .questionType)
        try container.encodeIfPresent(options, forKey: .options)
        try container.encode(correctAnswerIndex, forKey: .correctAnswerIndex)
        try container.encode(points, forKey: .points)
        try container.encodeIfPresent(explanation, forKey: .explanation)
        try container.encodeIfPresent(stepByStepSolution, forKey: .stepByStepSolution)
        // For backend compatibility: always send correctAnswer as the answer text
        if let options = options, correctAnswerIndex < options.count {
            try container.encode(options[correctAnswerIndex], forKey: .correctAnswer)
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case questionText
        case questionType
        case options
        case correctAnswerIndex
        case correctAnswer // For backward compatibility
        case points
        case explanation
        case stepByStepSolution
    }
}

// Model for an exercise
struct Exercise: Identifiable, Codable {
    let id: UUID
    var title: String
    var topic: String
    var subtopic: String
    var classroomIds: [String] // Classrooms this exercise is assigned to (can be multiple)
    var questions: [Question]
    var createdAt: Date
    var createdBy: String // Teacher's ID
    var dueDate: Date // Due time for the exercise
    
    // MongoDB ObjectId (ignored for iOS, but needed for backend compatibility)
    var mongoId: String?
    
    // New fields from backend
    var canSubmit: Bool?
    var isPastDue: Bool?
    
    init(id: UUID = UUID(), title: String, topic: String, subtopic: String, classroomIds: [String], questions: [Question], createdBy: String, createdAt: Date = Date(), dueDate: Date, mongoId: String? = nil, canSubmit: Bool? = nil, isPastDue: Bool? = nil) {
        self.id = id
        self.title = title
        self.topic = topic
        self.subtopic = subtopic
        self.classroomIds = classroomIds
        self.questions = questions
        self.createdAt = createdAt
        self.createdBy = createdBy
        self.dueDate = dueDate
        self.mongoId = mongoId
        self.canSubmit = canSubmit
        self.isPastDue = isPastDue
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case topic
        case subtopic
        case classroomIds
        case questions
        case createdAt
        case createdBy
        case dueDate
        case mongoId = "_id"
        case canSubmit
        case isPastDue
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        let idString = try container.decode(String.self, forKey: .id)
        guard let uuid = UUID(uuidString: idString) else {
            throw DecodingError.dataCorruptedError(
                forKey: .id,
                in: container,
                debugDescription: "Invalid UUID string: \(idString)"
            )
        }
        self.id = uuid
        self.title = try container.decode(String.self, forKey: .title)
        self.topic = try container.decode(String.self, forKey: .topic)
        self.subtopic = try container.decode(String.self, forKey: .subtopic)
        self.classroomIds = try container.decode([String].self, forKey: .classroomIds)
        self.questions = try container.decode([Question].self, forKey: .questions)
        self.createdBy = try container.decode(String.self, forKey: .createdBy)
        self.mongoId = try container.decodeIfPresent(String.self, forKey: .mongoId)
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        self.createdAt = try Exercise.parseDate(createdAtString)
        let dueDateString = try container.decode(String.self, forKey: .dueDate)
        self.dueDate = try Exercise.parseDate(dueDateString)
        self.canSubmit = try container.decodeIfPresent(Bool.self, forKey: .canSubmit)
        self.isPastDue = try container.decodeIfPresent(Bool.self, forKey: .isPastDue)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id.uuidString, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(topic, forKey: .topic)
        try container.encode(subtopic, forKey: .subtopic)
        try container.encode(classroomIds, forKey: .classroomIds)
        try container.encode(questions, forKey: .questions)
        try container.encode(createdBy, forKey: .createdBy)
        let isoFormatter = ISO8601DateFormatter()
        try container.encode(isoFormatter.string(from: createdAt), forKey: .createdAt)
        try container.encode(isoFormatter.string(from: dueDate), forKey: .dueDate)
        if let mongoId = mongoId {
            try container.encode(mongoId, forKey: .mongoId)
        }
        try container.encodeIfPresent(canSubmit, forKey: .canSubmit)
        try container.encodeIfPresent(isPastDue, forKey: .isPastDue)
    }
    
    // Helper method to parse various date formats from backend
    static func parseDate(_ dateString: String) throws -> Date {
        ExerciseLogger.shared.log("Parsing date string: \(dateString)", type: .debug)
        
        // Try multiple date formats to handle backend variations
        let formatters: [(DateFormatter, String)] = [
            // Format 1: "2025-06-23T03:58:46.971410+00:00"
            ({
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXXXX"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(), "microseconds with timezone"),
            
            // Format 2: "2025-06-24T03:58:29Z"
            ({
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(), "seconds with Z"),
            
            // Format 3: Standard ISO8601
            ({
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXXXX"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(), "ISO8601"),
            
            // Format 4: ISO8601 with milliseconds
            ({
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSXXXXX"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(), "ISO8601 with milliseconds")
        ]
        
        for (formatter, description) in formatters {
            if let date = formatter.date(from: dateString) {
                ExerciseLogger.shared.log("Successfully parsed date using \(description): \(date)", type: .debug)
                return date
            }
        }
        
        // Fallback: Try ISO8601DateFormatter
        let iso8601Formatter = ISO8601DateFormatter()
        if let date = iso8601Formatter.date(from: dateString) {
            ExerciseLogger.shared.log("Successfully parsed date using ISO8601DateFormatter: \(date)", type: .debug)
            return date
        }
        
        ExerciseLogger.shared.log("Failed to parse date string: \(dateString)", type: .error)
        throw DecodingError.dataCorrupted(
            DecodingError.Context(
                codingPath: [],
                debugDescription: "Unable to parse date: \(dateString)"
            )
        )
    }
}

// Model for student's submission to an exercise
struct StudentSubmission: Identifiable, Codable {
    let id: UUID
    let exerciseId: UUID
    let studentId: String
    var answers: [QuestionSubmission]
    var startTime: Date
    var endTime: Date?
    var score: Double?
    
    // *** NEW: Additional fields from backend ***
    var mongoId: String? // Backend's MongoDB _id
    var isLateSubmission: Bool?
    var earnedPoints: Double?
    var totalPoints: Double?
    var gradingStatus: String?
    var exerciseTitle: String?
    var attempts: Int?
    
    init(id: UUID = UUID(), exerciseId: UUID, studentId: String, answers: [QuestionSubmission] = [], startTime: Date = Date()) {
        self.id = id
        self.exerciseId = exerciseId
        self.studentId = studentId
        self.answers = answers
        self.startTime = startTime
    }
    
    // Custom decoder to handle backend format
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Decode UUIDs from strings
        let idString = try container.decode(String.self, forKey: .id)
        guard let uuid = UUID(uuidString: idString) else {
            throw DecodingError.dataCorruptedError(
                forKey: .id,
                in: container,
                debugDescription: "Invalid UUID string for submission: \(idString)"
            )
        }
        self.id = uuid
        
        let exerciseIdString = try container.decode(String.self, forKey: .exerciseId)
        guard let exerciseUuid = UUID(uuidString: exerciseIdString) else {
            throw DecodingError.dataCorruptedError(
                forKey: .exerciseId,
                in: container,
                debugDescription: "Invalid UUID string for exercise: \(exerciseIdString)"
            )
        }
        self.exerciseId = exerciseUuid
        
        // Decode required fields
        self.studentId = try container.decode(String.self, forKey: .studentId)
        self.answers = try container.decode([QuestionSubmission].self, forKey: .answers)
        
        // Decode optional fields
        self.score = try container.decodeIfPresent(Double.self, forKey: .score)
        self.mongoId = try container.decodeIfPresent(String.self, forKey: .mongoId)
        self.isLateSubmission = try container.decodeIfPresent(Bool.self, forKey: .isLateSubmission)
        self.earnedPoints = try container.decodeIfPresent(Double.self, forKey: .earnedPoints)
        self.totalPoints = try container.decodeIfPresent(Double.self, forKey: .totalPoints)
        self.gradingStatus = try container.decodeIfPresent(String.self, forKey: .gradingStatus)
        self.exerciseTitle = try container.decodeIfPresent(String.self, forKey: .exerciseTitle)
        self.attempts = try container.decodeIfPresent(Int.self, forKey: .attempts)
        
        // Custom date parsing
        let startTimeString = try container.decode(String.self, forKey: .startTime)
        self.startTime = try Exercise.parseDate(startTimeString)
        
        if let endTimeString = try container.decodeIfPresent(String.self, forKey: .endTime) {
            self.endTime = try Exercise.parseDate(endTimeString)
        } else {
            self.endTime = nil
        }
    }
    
    // Custom encoder to maintain compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // Encode UUIDs as strings
        try container.encode(id.uuidString, forKey: .id)
        try container.encode(exerciseId.uuidString, forKey: .exerciseId)
        try container.encode(studentId, forKey: .studentId)
        try container.encode(answers, forKey: .answers)
        try container.encodeIfPresent(score, forKey: .score)
        
        // Encode additional fields if present
        try container.encodeIfPresent(mongoId, forKey: .mongoId)
        try container.encodeIfPresent(isLateSubmission, forKey: .isLateSubmission)
        try container.encodeIfPresent(earnedPoints, forKey: .earnedPoints)
        try container.encodeIfPresent(totalPoints, forKey: .totalPoints)
        try container.encodeIfPresent(gradingStatus, forKey: .gradingStatus)
        try container.encodeIfPresent(exerciseTitle, forKey: .exerciseTitle)
        try container.encodeIfPresent(attempts, forKey: .attempts)
        
        // Encode dates as ISO8601 strings
        let isoFormatter = ISO8601DateFormatter()
        try container.encode(isoFormatter.string(from: startTime), forKey: .startTime)
        if let endTime = endTime {
            try container.encode(isoFormatter.string(from: endTime), forKey: .endTime)
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case exerciseId
        case studentId
        case answers
        case startTime
        case endTime
        case score
        case mongoId = "_id"
        case isLateSubmission
        case earnedPoints
        case totalPoints
        case gradingStatus
        case exerciseTitle
        case attempts
    }
}

// Model for a single question submission
struct QuestionSubmission: Identifiable, Codable {
    let id: UUID
    let questionId: UUID
    var answer: String
    var isCorrect: Bool?
    var pointsEarned: Double?
    
    // *** NEW: Additional fields from backend ***
    var needsManualGrading: Bool?
    
    init(id: UUID = UUID(), questionId: UUID, answer: String) {
        self.id = id
        self.questionId = questionId
        self.answer = answer
    }
    
    // Custom decoder to handle backend format
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Handle missing id field from backend - generate one if not present
        if let idString = try container.decodeIfPresent(String.self, forKey: .id),
           let uuid = UUID(uuidString: idString) {
            self.id = uuid
        } else {
            // Generate a new UUID if backend doesn't provide one
            self.id = UUID()
        }
        
        let questionIdString = try container.decode(String.self, forKey: .questionId)
        guard let questionUuid = UUID(uuidString: questionIdString) else {
            throw DecodingError.dataCorruptedError(
                forKey: .questionId,
                in: container,
                debugDescription: "Invalid UUID string for question: \(questionIdString)"
            )
        }
        self.questionId = questionUuid
        
        // Decode other fields
        self.answer = try container.decode(String.self, forKey: .answer)
        self.isCorrect = try container.decodeIfPresent(Bool.self, forKey: .isCorrect)
        self.pointsEarned = try container.decodeIfPresent(Double.self, forKey: .pointsEarned)
        self.needsManualGrading = try container.decodeIfPresent(Bool.self, forKey: .needsManualGrading)
    }
    
    // Custom encoder to maintain compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // Encode UUIDs as strings
        try container.encode(id.uuidString, forKey: .id)
        try container.encode(questionId.uuidString, forKey: .questionId)
        try container.encode(answer, forKey: .answer)
        try container.encodeIfPresent(isCorrect, forKey: .isCorrect)
        try container.encodeIfPresent(pointsEarned, forKey: .pointsEarned)
        try container.encodeIfPresent(needsManualGrading, forKey: .needsManualGrading)
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case questionId
        case answer
        case isCorrect
        case pointsEarned
        case needsManualGrading
    }
}

// *** NEW: Model for a single question grade ***
struct QuestionGrade {
    let questionId: UUID
    let pointsEarned: Double
    let isCorrect: Bool
    let feedback: String
    
    init(questionId: UUID, pointsEarned: Double, isCorrect: Bool, feedback: String = "") {
        self.questionId = questionId
        self.pointsEarned = pointsEarned
        self.isCorrect = isCorrect
        self.feedback = feedback
    }
}

// ViewModel for managing exercises
class ExerciseViewModel: ObservableObject {
    @Published var exercises: [Exercise] = []
    @Published var studentSubmissions: [StudentSubmission] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    // MARK: - Submission Loading State Management
    @Published var isLoadingSubmissions = false
    @Published var lastSubmissionLoadTime: Date?
    private let submissionCacheTimeout: TimeInterval = 30 // 30 seconds cache
    
    // Global cache for exercise-specific submissions to prevent duplicate API calls
    private var exerciseSubmissionsCache: [UUID: ([StudentSubmission], Date)] = [:]
    
    // Public getter for exercise submissions cache (for views to check if data is already loaded)
    func getCachedSubmissions(for exerciseId: UUID) -> [StudentSubmission]? {
        if let (submissions, timestamp) = exerciseSubmissionsCache[exerciseId],
           Date().timeIntervalSince(timestamp) < submissionCacheTimeout {
            ExerciseLogger.shared.log("Returning cached submissions for exercise \(exerciseId.uuidString)", type: .debug)
            return submissions
        }
        return nil
    }
    
    // Check if submissions need refreshing based on cache timeout
    private var shouldRefreshSubmissions: Bool {
        guard let lastLoad = lastSubmissionLoadTime else { return true }
        return Date().timeIntervalSince(lastLoad) > submissionCacheTimeout
    }
    
    // Check if exercise-specific submissions need refreshing
    private func shouldRefreshExerciseSubmissions(for exerciseId: UUID) -> Bool {
        guard let (_, timestamp) = exerciseSubmissionsCache[exerciseId] else { return true }
        return Date().timeIntervalSince(timestamp) > submissionCacheTimeout
    }
    
    init() {
        // Add sample exercises for testing
    }
    
    // Load all exercises from backend server
    func getAllExercises() async throws {
        ExerciseLogger.shared.log("Starting getAllExercises operation", type: .info)
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)"
            ExerciseLogger.shared.log("Fetching exercises from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            // Create request with authentication
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString, 
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("Successfully received data, attempting to decode exercises", type: .info)
                
                do {
                    // Use default decoder since we have custom date parsing in Exercise model
                    let decoder = JSONDecoder()
                    
                    let exercises = try decoder.decode([Exercise].self, from: data)
                    ExerciseLogger.shared.log("Successfully decoded \(exercises.count) exercises", type: .info)
                    
                    // Log each exercise for debugging
                    for (index, exercise) in exercises.enumerated() {
                        ExerciseLogger.shared.log("Exercise \(index + 1): \(exercise.title) (ID: \(exercise.id.uuidString))", type: .debug)
                    }
                    
                    await MainActor.run {
                        self.exercises = exercises
                        isLoading = false
                    }
                    ExerciseLogger.shared.log("getAllExercises completed successfully", type: .info)
                } catch {
                    ExerciseLogger.shared.log("Failed to decode exercises: \(error.localizedDescription)", type: .error)
                    ExerciseLogger.shared.log("Raw response data: \(String(data: data, encoding: .utf8) ?? "Unable to convert to string")", type: .debug)
                    throw error
                }
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                }
                
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                let detailedMessage = "Server Error (\(httpResponse.statusCode)): \(responseString)"
                throw NSError(domain: "NetworkError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: detailedMessage])
            }
        } catch {
            ExerciseLogger.shared.log("getAllExercises failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // Create or update an exercise - sends JSON to backend server
    func createExercise(_ exercise: Exercise) async throws {
        ExerciseLogger.shared.log("Starting createExercise operation", type: .info)
        ExerciseLogger.shared.logExerciseData(exercise, operation: "CREATE")
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)"
            ExerciseLogger.shared.log("Creating exercise at URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            // Convert Exercise object to JSON with custom date formatting
            ExerciseLogger.shared.log("Encoding exercise data to JSON", type: .info)
            
            // Use default encoder since we have custom date encoding in Exercise model
            let encoder = JSONEncoder()
            
            let jsonData = try encoder.encode(exercise)
            ExerciseLogger.shared.log("Successfully encoded exercise data (\(jsonData.count) bytes)", type: .info)
            
            // Log the JSON string to see the exact date format being sent
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                ExerciseLogger.shared.log("JSON being sent: \(jsonString)", type: .debug)
            }
            
            request.httpBody = jsonData
            
            // Prepare headers for logging
            var requestHeaders = ["Content-Type": "application/json"]
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                requestHeaders.merge(authHeaders) { (_, new) in new }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "POST",
                headers: requestHeaders,
                body: jsonData
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 201 {
                ExerciseLogger.shared.log("Exercise created successfully (status 201)", type: .info)
                
                // Log the raw response data first to see what we're trying to decode
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Raw server response: \(responseString)", type: .debug)
                }
                
                // First try to decode as a full Exercise object
                do {
                    let decoder = JSONDecoder()
                    
                    let createdExercise = try decoder.decode(Exercise.self, from: data)
                    ExerciseLogger.shared.log("Successfully decoded created exercise with ID: \(createdExercise.id.uuidString)", type: .info)
                    
                    await MainActor.run {
                        if let index = exercises.firstIndex(where: { $0.id == exercise.id }) {
                            // Update existing exercise
                            ExerciseLogger.shared.log("Updating existing exercise at index \(index)", type: .info)
                            exercises[index] = createdExercise
                        } else {
                            // Add new exercise
                            ExerciseLogger.shared.log("Adding new exercise to exercises array", type: .info)
                            exercises.append(createdExercise)
                        }
                        isLoading = false
                    }
                    ExerciseLogger.shared.log("createExercise completed successfully", type: .info)
                    return // Success!
                    
                } catch {
                    ExerciseLogger.shared.log("Failed to decode as Exercise object, trying alternative formats...", type: .info)
                    ExerciseLogger.shared.log("Decode error: \(error.localizedDescription)", type: .debug)
                }
                
                // Try to decode as a simple success response
                do {
                    if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        ExerciseLogger.shared.log("Server returned JSON object: \(jsonObject)", type: .debug)
                        
                        // Check for common success response patterns
                        if let success = jsonObject["success"] as? Bool, success {
                            ExerciseLogger.shared.log("Server returned simple success response", type: .info)
                        } else if let id = jsonObject["id"] as? String {
                            ExerciseLogger.shared.log("Server returned ID: \(id)", type: .info)
                        } else if let message = jsonObject["message"] as? String {
                            ExerciseLogger.shared.log("Server returned message: \(message)", type: .info)
                        }
                        
                        // Since server doesn't return the full object, just add our original exercise to the list
                        await MainActor.run {
                            if !exercises.contains(where: { $0.id == exercise.id }) {
                                exercises.append(exercise)
                                ExerciseLogger.shared.log("Added original exercise to local list", type: .info)
                            }
                            isLoading = false
                        }
                        ExerciseLogger.shared.log("createExercise completed with simple response", type: .info)
                        return // Success!
                    }
                } catch {
                    ExerciseLogger.shared.log("Failed to decode as JSON object: \(error.localizedDescription)", type: .debug)
                }
                
                // If all else fails, treat as success but log the issue
                ExerciseLogger.shared.log("Unable to decode server response, but got 201 status - treating as success", type: .info)
                await MainActor.run {
                    if !exercises.contains(where: { $0.id == exercise.id }) {
                        exercises.append(exercise)
                    }
                    isLoading = false
                }
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                
                // Handle error response from backend server
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                
                // Try to parse structured error message
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorData["error"] as? String {
                    ExerciseLogger.shared.log("Structured server error message: \(errorMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: "Server Error (\(httpResponse.statusCode)): \(errorMessage)",
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                } else {
                    // Provide detailed error with status code and response
                    let detailedMessage = "Server Error (\(httpResponse.statusCode)): \(responseString)"
                    ExerciseLogger.shared.log("Unstructured server error: \(detailedMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: detailedMessage,
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                }
            }
        } catch {
            ExerciseLogger.shared.log("createExercise failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // Get exercises for a specific classroom - sends JSON request to backend server
    func getExercises(for classroomId: String) async throws {
        ExerciseLogger.shared.log("Starting getExercises operation for classroom: \(classroomId)", type: .info)
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)?classroomId=\(classroomId)"
            ExerciseLogger.shared.log("Fetching exercises for classroom from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            // Create request with authentication
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString, 
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("Successfully received data, attempting to decode exercises for classroom \(classroomId)", type: .info)
                
                do {
                    // Create custom JSON decoder with consistent date formatting
                    let decoder = JSONDecoder()
                    
                    let exercises = try decoder.decode([Exercise].self, from: data)
                    ExerciseLogger.shared.log("Successfully decoded \(exercises.count) exercises for classroom \(classroomId)", type: .info)
                    
                    // Log each exercise for debugging
                    for (index, exercise) in exercises.enumerated() {
                        ExerciseLogger.shared.log("Exercise \(index + 1): \(exercise.title) (ID: \(exercise.id.uuidString))", type: .debug)
                    }
                    
                    await MainActor.run {
                        self.exercises = exercises
                        isLoading = false
                    }
                    ExerciseLogger.shared.log("getExercises for classroom \(classroomId) completed successfully", type: .info)
                } catch {
                    ExerciseLogger.shared.log("Failed to decode exercises for classroom \(classroomId): \(error.localizedDescription)", type: .error)
                    ExerciseLogger.shared.log("Raw response data: \(String(data: data, encoding: .utf8) ?? "Unable to convert to string")", type: .debug)
                    throw error
                }
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                }
                
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                let detailedMessage = "Server Error (\(httpResponse.statusCode)): \(responseString)"
                throw NSError(domain: "NetworkError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: detailedMessage])
            }
        } catch {
            ExerciseLogger.shared.log("getExercises for classroom \(classroomId) failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // Submit a student's submission - sends JSON to backend server
    func submitSubmission(_ submission: StudentSubmission) async throws {
        ExerciseLogger.shared.log("Starting submitSubmission operation", type: .info)
        ExerciseLogger.shared.logSubmissionData(submission, operation: "SUBMIT")
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = APIConfig.submissionEndpoint
            ExerciseLogger.shared.log("Submitting to URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            // Create submission data in the new API format
            ExerciseLogger.shared.log("Creating submission data in new API format", type: .info)
            
            let submissionData: [String: Any] = [
                "exerciseId": submission.exerciseId.uuidString,
                "answers": submission.answers.map { answer in
                    [
                        "questionId": answer.questionId.uuidString,
                        "answer": answer.answer
                    ]
                }
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: submissionData)
            ExerciseLogger.shared.log("Successfully encoded submission data (\(jsonData.count) bytes)", type: .info)
            
            // Log the JSON for debugging
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                ExerciseLogger.shared.log("Submission JSON: \(jsonString)", type: .debug)
            }
            
            request.httpBody = jsonData
            
            // Prepare headers for logging
            var requestHeaders = ["Content-Type": "application/json"]
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                requestHeaders.merge(authHeaders) { (_, new) in new }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "POST",
                headers: requestHeaders,
                body: jsonData
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 201 {
                ExerciseLogger.shared.log("Submission created successfully (status 201)", type: .info)
                
                // Parse enhanced response format
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Enhanced response body: \(responseString)", type: .debug)
                }
                
                do {
                    // Try to parse enhanced response format first
                    if let submissionResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        ExerciseLogger.shared.log("Successfully parsed enhanced submission response", type: .info)
                        
                        // Log enhanced response details
                        if let score = submissionResponse["score"] as? Double {
                            ExerciseLogger.shared.log("Submission score: \(score)", type: .info)
                        }
                        
                        if let gradingStatus = submissionResponse["gradingStatus"] as? String {
                            ExerciseLogger.shared.log("Grading status: \(gradingStatus)", type: .info)
                        }
                        
                        if let summary = submissionResponse["summary"] as? [String: Any] {
                            let autoGraded = summary["autoGraded"] as? Bool ?? false
                            let correctAnswers = summary["multipleChoiceCorrect"] as? Int ?? 0
                            ExerciseLogger.shared.log("Auto-graded: \(autoGraded), Correct answers: \(correctAnswers)", type: .info)
                        }
                        
                        // Create a basic StudentSubmission for local tracking if needed
                        var updatedSubmission = submission
                        updatedSubmission.endTime = Date()
                        if let score = submissionResponse["score"] as? Double {
                            updatedSubmission.score = score
                        }
                        
                        await MainActor.run {
                            studentSubmissions.append(updatedSubmission)
                            isLoading = false
                        }
                        ExerciseLogger.shared.log("submitSubmission completed successfully with enhanced response", type: .info)
                        
                    } else {
                        // Fallback: try to decode as StudentSubmission (for backward compatibility)
                        let savedSubmission = try JSONDecoder().decode(StudentSubmission.self, from: data)
                        ExerciseLogger.shared.log("Successfully decoded saved submission with ID: \(savedSubmission.id.uuidString)", type: .info)
                        
                        await MainActor.run {
                            studentSubmissions.append(savedSubmission)
                            isLoading = false
                        }
                        ExerciseLogger.shared.log("submitSubmission completed successfully (fallback)", type: .info)
                    }
                } catch {
                    ExerciseLogger.shared.log("Failed to decode submission response: \(error.localizedDescription)", type: .error)
                    ExerciseLogger.shared.log("Raw response data: \(String(data: data, encoding: .utf8) ?? "Unable to convert to string")", type: .debug)
                    
                    // If decoding fails but we got 201, still consider it a success
                    var updatedSubmission = submission
                    updatedSubmission.endTime = Date()
                    await MainActor.run {
                        studentSubmissions.append(updatedSubmission)
                        isLoading = false
                    }
                    ExerciseLogger.shared.log("submitSubmission completed with parsing warning", type: .info)
                }
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                
                // Enhanced error handling based on API update summary
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                
                // Handle specific HTTP status codes
                switch httpResponse.statusCode {
                case 400:
                    ExerciseLogger.shared.log("❌ Bad Request - Check submission data format", type: .error)
                case 401:
                    ExerciseLogger.shared.log("❌ Unauthorized - Check JWT token", type: .error)
                case 403:
                    ExerciseLogger.shared.log("❌ Forbidden - Access denied", type: .error)
                case 404:
                    ExerciseLogger.shared.log("❌ Not Found - Check API endpoint: \(urlString)", type: .error)
                case 409:
                    ExerciseLogger.shared.log("❌ Conflict - Already submitted", type: .error)
                default:
                    ExerciseLogger.shared.log("❌ HTTP Error: \(httpResponse.statusCode)", type: .error)
                }
                
                // Try to parse structured error message
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorData["error"] as? String {
                    ExerciseLogger.shared.log("Structured server error message: \(errorMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: "Submission Error (\(httpResponse.statusCode)): \(errorMessage)",
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                } else {
                    // Provide detailed error with status code and response
                    let detailedMessage = "Submission Error (\(httpResponse.statusCode)): \(responseString)"
                    ExerciseLogger.shared.log("Unstructured server error: \(detailedMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: detailedMessage,
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                }
            }
        } catch {
            // Handle cancellation errors differently from real errors
            if error is CancellationError {
                ExerciseLogger.shared.log("submitSubmission was cancelled - operation was interrupted", type: .debug)
                await MainActor.run {
                    isLoading = false
                    // Don't set error state for cancellation
                }
                throw error
            } else {
                // Log real errors as errors
                ExerciseLogger.shared.log("submitSubmission failed with error: \(error.localizedDescription)", type: .error)
                await MainActor.run {
                    self.error = error
                    isLoading = false
                }
                throw error
            }
        }
    }
    
    // Get student's submissions for an exercise - sends JSON request to backend server
    func getStudentSubmissions(for exerciseId: UUID, forceRefresh: Bool = false) async throws -> [StudentSubmission] {
        // If forceRefresh is true, skip cache and fetch from backend
        if !forceRefresh, !shouldRefreshExerciseSubmissions(for: exerciseId) {
            let (cachedSubmissions, _) = exerciseSubmissionsCache[exerciseId]!
            ExerciseLogger.shared.log("Using cached submissions for exercise \(exerciseId.uuidString) - \(cachedSubmissions.count) submissions", type: .debug)
            return cachedSubmissions
        }
        
        ExerciseLogger.shared.log("Starting getStudentSubmissions operation for exercise ID: \(exerciseId.uuidString)", type: .info)
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.submissionEndpoint)?exerciseId=\(exerciseId.uuidString)"
            ExerciseLogger.shared.log("Fetching submissions from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            // Create request with authentication
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString, 
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("Successfully received data, attempting to decode submissions", type: .info)
                
                do {
                    let decoder = JSONDecoder()
                    
                    // Log the raw JSON for debugging before decoding
                    if let responseString = String(data: data, encoding: .utf8) {
                        ExerciseLogger.shared.log("Raw submission response before decoding: \(responseString)", type: .debug)
                    }
                    
                    let submissions = try decoder.decode([StudentSubmission].self, from: data)
                    ExerciseLogger.shared.log("Successfully decoded \(submissions.count) submissions for exercise \(exerciseId.uuidString)", type: .info)
                    
                    // Log each submission for debugging
                    for (index, submission) in submissions.enumerated() {
                        ExerciseLogger.shared.log("Submission \(index + 1): Student \(submission.studentId) (ID: \(submission.id.uuidString))", type: .debug)
                        ExerciseLogger.shared.log("  - MongoDB ObjectId: \(submission.mongoId ?? "nil")", type: .debug)
                        ExerciseLogger.shared.log("  - Exercise ID: \(submission.exerciseId.uuidString)", type: .debug)
                        ExerciseLogger.shared.log("  - Score: \(submission.score?.description ?? "nil")", type: .debug)
                        ExerciseLogger.shared.log("  - Start Time: \(submission.startTime)", type: .debug)
                        ExerciseLogger.shared.log("  - End Time: \(submission.endTime?.description ?? "nil")", type: .debug)
                        ExerciseLogger.shared.log("  - Answers Count: \(submission.answers.count)", type: .debug)
                        ExerciseLogger.shared.log("  - Grading Status: \(submission.gradingStatus ?? "nil")", type: .debug)
                        ExerciseLogger.shared.log("  - Exercise Title: \(submission.exerciseTitle ?? "nil")", type: .debug)
                        
                        // Log each answer
                        for (answerIndex, answer) in submission.answers.enumerated() {
                            ExerciseLogger.shared.log("    Answer \(answerIndex + 1): \(answer.answer)", type: .debug)
                            ExerciseLogger.shared.log("      - Question ID: \(answer.questionId.uuidString)", type: .debug)
                            ExerciseLogger.shared.log("      - Is Correct: \(answer.isCorrect?.description ?? "nil")", type: .debug)
                            ExerciseLogger.shared.log("      - Points Earned: \(answer.pointsEarned?.description ?? "nil")", type: .debug)
                            ExerciseLogger.shared.log("      - Needs Manual Grading: \(answer.needsManualGrading?.description ?? "nil")", type: .debug)
                        }
                    }
                    
                    // Cache the results
                    exerciseSubmissionsCache[exerciseId] = (submissions, Date())
                    
                    await MainActor.run {
                        isLoading = false
                    }
                    ExerciseLogger.shared.log("getStudentSubmissions completed successfully", type: .info)
                    return submissions
                } catch {
                    ExerciseLogger.shared.log("Failed to decode submissions: \(error.localizedDescription)", type: .error)
                    ExerciseLogger.shared.log("Raw response data: \(String(data: data, encoding: .utf8) ?? "Unable to convert to string")", type: .debug)
                    
                    // Additional debugging for decoding error
                    if let decodingError = error as? DecodingError {
                        ExerciseLogger.shared.log("Decoding error details:", type: .error)
                        switch decodingError {
                        case .dataCorrupted(let context):
                            ExerciseLogger.shared.log("Data corrupted: \(context.debugDescription)", type: .error)
                        case .keyNotFound(let key, let context):
                            ExerciseLogger.shared.log("Key '\(key.stringValue)' not found: \(context.debugDescription)", type: .error)
                        case .typeMismatch(let type, let context):
                            ExerciseLogger.shared.log("Type mismatch for \(type): \(context.debugDescription)", type: .error)
                        case .valueNotFound(let type, let context):
                            ExerciseLogger.shared.log("Value not found for \(type): \(context.debugDescription)", type: .error)
                        @unknown default:
                            ExerciseLogger.shared.log("Unknown decoding error: \(error.localizedDescription)", type: .error)
                        }
                    }
                    
                    throw error
                }
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                }
                
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                let detailedMessage = "Server Error (\(httpResponse.statusCode)): \(responseString)"
                throw NSError(domain: "NetworkError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: detailedMessage])
            }
        } catch {
            // Handle cancellation errors differently from real errors
            if error is CancellationError {
                ExerciseLogger.shared.log("getStudentSubmissions was cancelled - view was likely dismissed", type: .debug)
                await MainActor.run {
                    isLoading = false
                    // Don't set error state for cancellation
                }
                throw error
            } else if (error as NSError).domain == "NSURLErrorDomain" && (error as NSError).code == NSURLErrorCancelled {
                // Handle URLSession cancellation
                ExerciseLogger.shared.log("getStudentSubmissions network request was cancelled", type: .debug)
                await MainActor.run {
                    isLoading = false
                    // Don't set error state for cancellation
                }
                throw CancellationError()
            } else {
                // Check if this is a cancellation-related error before logging as error
                let errorMessage = error.localizedDescription.lowercased()
                if errorMessage.contains("cancelled") || errorMessage.contains("canceled") {
                    // Log cancellation as debug, not error
                    ExerciseLogger.shared.log("getStudentSubmissions was cancelled: \(error.localizedDescription)", type: .debug)
                    await MainActor.run {
                        isLoading = false
                        // Don't set error state for cancellation
                    }
                    throw CancellationError()
                } else {
                    // Log real errors as errors
                    ExerciseLogger.shared.log("getStudentSubmissions failed with error: \(error.localizedDescription)", type: .error)
                    await MainActor.run {
                        self.error = error
                        isLoading = false
                    }
                    throw error
                }
            }
        }
    }
    
    // Grade a student submission - sends JSON to backend server
    func gradeSubmission(submission: StudentSubmission, score: Double, feedback: String, gradingMethod: String = "manual") async throws {
        ExerciseLogger.shared.log("=== TEACHER GRADING OPERATION STARTED ===", type: .info)
        ExerciseLogger.shared.log("🎯 Teacher is updating student grade", type: .info)
        ExerciseLogger.shared.log("📝 Submission UUID: \(submission.id.uuidString)", type: .info)
        ExerciseLogger.shared.log("📝 Submission MongoDB ID: \(submission.mongoId ?? "Not available")", type: .info)
        ExerciseLogger.shared.log("📊 Score: \(score)", type: .info)
        ExerciseLogger.shared.log("💬 Feedback: \(feedback)", type: .info)
        ExerciseLogger.shared.log("🔧 Grading Method: \(gradingMethod)", type: .info)
        ExerciseLogger.shared.log("👨‍🏫 Teacher ID: \(UserManager.shared.currentUser.id)", type: .info)
        ExerciseLogger.shared.log("👨‍🏫 Teacher Name: \(UserManager.shared.currentUser.name)", type: .info)
        
        // Ensure we have the MongoDB ObjectId for the backend
        guard let mongoId = submission.mongoId else {
            ExerciseLogger.shared.log("❌ ERROR: No MongoDB ObjectId available for submission", type: .error)
            throw NSError(domain: "SubmissionError", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Cannot grade submission: MongoDB ObjectId is missing. Please refresh the submission data."
            ])
        }
        
        ExerciseLogger.shared.log("✅ Using MongoDB ObjectId for backend: \(mongoId)", type: .info)
        
        // Ensure valid authentication and get authorization header
        var authHeaders: [String: String]
        
        ExerciseLogger.shared.log("🔐 Starting authentication check for grading...", type: .info)
        ExerciseLogger.shared.log("👤 Current user: \(UserManager.shared.currentUser.name) (ID: \(UserManager.shared.currentUser.id))", type: .info)
        ExerciseLogger.shared.log("🔑 Is logged in: \(UserManager.shared.isLoggedIn)", type: .info)
        
        if let headers = UserManager.shared.getAuthorizationHeader() {
            authHeaders = headers
            ExerciseLogger.shared.log("✅ Authorization header available", type: .info)
            ExerciseLogger.shared.log("📋 Auth headers: \(headers)", type: .debug)
        } else {
            ExerciseLogger.shared.log("❌ ERROR: No authorization header available, attempting to refresh authentication", type: .error)
            
            // Try to ensure valid authentication first
            ExerciseLogger.shared.log("🔄 Attempting to ensure valid authentication...", type: .info)
            let authValid = await UserManager.shared.ensureValidAuthentication()
            ExerciseLogger.shared.log("🔍 Authentication validation result: \(authValid)", type: .info)
            
            if authValid {
                // Try again after ensuring authentication
                ExerciseLogger.shared.log("🔄 Trying to get authorization header again after validation...", type: .info)
                if let retryAuthHeaders = UserManager.shared.getAuthorizationHeader() {
                    authHeaders = retryAuthHeaders
                    ExerciseLogger.shared.log("✅ Authorization header available after authentication refresh", type: .info)
                    ExerciseLogger.shared.log("📋 Retry auth headers: \(retryAuthHeaders)", type: .debug)
                } else {
                    ExerciseLogger.shared.log("❌ Still no authorization header after authentication refresh", type: .error)
                    throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                        NSLocalizedDescriptionKey: "Authorization header is missing. Please log in again."
                    ])
                }
            } else {
                ExerciseLogger.shared.log("❌ Authentication validation failed", type: .error)
                throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                    NSLocalizedDescriptionKey: "Authentication failed. Please log in again."
                ])
            }
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.submissionEndpoint)?submission_id=\(mongoId)"
            ExerciseLogger.shared.log("🌐 Constructing grading URL", type: .info)
            ExerciseLogger.shared.log("🔗 Base endpoint: \(APIConfig.submissionEndpoint)", type: .debug)
            ExerciseLogger.shared.log("🔗 Final URL: \(urlString)", type: .info)
            ExerciseLogger.shared.log("📋 URL breakdown:", type: .debug)
            ExerciseLogger.shared.log("   - Base: \(APIConfig.baseURL)", type: .debug)
            ExerciseLogger.shared.log("   - Path: /submission", type: .debug)
            ExerciseLogger.shared.log("   - Query: submission_id=\(mongoId)", type: .debug)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "PUT"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            ExerciseLogger.shared.log("📤 Preparing PUT request for grading", type: .info)
            ExerciseLogger.shared.log("🔐 Adding authentication headers", type: .debug)
            
            // Add authorization header (we already verified it exists above)
            for (key, value) in authHeaders {
                request.setValue(value, forHTTPHeaderField: key)
                ExerciseLogger.shared.log("   ✅ Added auth header: \(key)", type: .debug)
            }
            
            // Create grading data JSON
            ExerciseLogger.shared.log("📝 Creating grading payload", type: .info)
            let gradingData: [String: Any] = [
                "score": score,
                "feedback": feedback,
                "gradedBy": UserManager.shared.currentUser.id,
                "gradingMethod": gradingMethod
            ]
            
            ExerciseLogger.shared.log("📊 Grading data created:", type: .debug)
            ExerciseLogger.shared.log("   - Score: \(score)", type: .debug)
            ExerciseLogger.shared.log("   - Feedback: \(feedback)", type: .debug)
            ExerciseLogger.shared.log("   - Graded By: \(UserManager.shared.currentUser.id)", type: .debug)
            ExerciseLogger.shared.log("   - Method: \(gradingMethod)", type: .debug)
            
            let jsonData = try JSONSerialization.data(withJSONObject: gradingData)
            ExerciseLogger.shared.log("✅ Successfully encoded grading data (\(jsonData.count) bytes)", type: .info)
            
            // Log the JSON payload for debugging
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                ExerciseLogger.shared.log("📄 JSON payload being sent:", type: .debug)
                ExerciseLogger.shared.log(jsonString, type: .debug)
            }
            
            request.httpBody = jsonData
            ExerciseLogger.shared.log("📦 Request body attached to request", type: .debug)
            
            // Prepare headers for logging
            var requestHeaders = ["Content-Type": "application/json"]
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                requestHeaders.merge(authHeaders) { (_, new) in new }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "PUT",
                headers: requestHeaders,
                body: jsonData
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("🎉 SUCCESS: Submission graded successfully!", type: .info)
                ExerciseLogger.shared.log("✅ HTTP Status: \(httpResponse.statusCode)", type: .info)
                ExerciseLogger.shared.log("📊 Grade updated for submission: \(mongoId)", type: .info)
                ExerciseLogger.shared.log("👨‍🏫 Graded by: \(UserManager.shared.currentUser.name) (\(UserManager.shared.currentUser.id))", type: .info)
                ExerciseLogger.shared.log("📈 Final score: \(score)", type: .info)
                
                await MainActor.run {
                    isLoading = false
                }
                ExerciseLogger.shared.log("=== TEACHER GRADING OPERATION COMPLETED SUCCESSFULLY ===", type: .info)
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                
                // Handle error response from backend server
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                
                // Try to parse structured error message
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorData["error"] as? String {
                    ExerciseLogger.shared.log("Structured server error message: \(errorMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: "Grading Error (\(httpResponse.statusCode)): \(errorMessage)",
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                } else {
                    // Provide detailed error with status code and response
                    let detailedMessage = "Grading Error (\(httpResponse.statusCode)): \(responseString)"
                    ExerciseLogger.shared.log("Unstructured server error: \(detailedMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: detailedMessage,
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                }
            }
        } catch {
            ExerciseLogger.shared.log("gradeSubmission failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
                // Convenience method for backward compatibility - finds submission by UUID
    func gradeSubmission(submissionId: UUID, score: Double, feedback: String, gradingMethod: String = "manual") async throws {
        ExerciseLogger.shared.log("🔍 Looking for submission with UUID: \(submissionId.uuidString)", type: .info)
        ExerciseLogger.shared.log("📊 Available submissions in cache: \(studentSubmissions.count)", type: .debug)
        
        // Log all available submission UUIDs for debugging
        for (index, sub) in studentSubmissions.enumerated() {
            ExerciseLogger.shared.log("  Submission \(index + 1): UUID=\(sub.id.uuidString), MongoID=\(sub.mongoId ?? "nil")", type: .debug)
        }
        
        // Find the submission in our cached data first
        if let submission = studentSubmissions.first(where: { $0.id == submissionId }) {
            ExerciseLogger.shared.log("✅ Found submission in cache with MongoDB ID: \(submission.mongoId ?? "nil")", type: .info)
            // Call the main grading method with the found submission
            try await gradeSubmission(submission: submission, score: score, feedback: feedback, gradingMethod: gradingMethod)
            return
        }
        
        // If not found in cache, try to load submissions for the specific exercise
        ExerciseLogger.shared.log("❌ Submission with UUID \(submissionId.uuidString) not found in cached data", type: .error)
        ExerciseLogger.shared.log("🔄 Attempting to load submissions for the exercise...", type: .info)
        
        // We need to find which exercise this submission belongs to
        // Since we don't have the exercise ID, we'll need to make a direct API call
        // This is a fallback for when the submission is not in our cache
        
        // Create a minimal submission object with just the UUID and try to grade it
        // The backend should be able to handle this if we provide the MongoDB ObjectId
        ExerciseLogger.shared.log("⚠️ WARNING: Submission not found in cache. This may happen when grading submissions from other students.", type: .info)
        ExerciseLogger.shared.log("💡 Tip: For better performance, load submissions for the specific exercise first.", type: .info)
        
        // For now, we'll throw an error asking the caller to provide the MongoDB ObjectId
        throw NSError(domain: "SubmissionError", code: 404, userInfo: [
            NSLocalizedDescriptionKey: "Submission not found in cache. Please use gradeSubmission(submission:score:feedback:gradingMethod:) with the full submission object, or load submissions for the exercise first."
        ])
    }
    
    // *** NEW: Update exercise - PUT /api/exercise/:id ***
    func updateExercise(_ exercise: Exercise) async throws {
        ExerciseLogger.shared.log("Starting updateExercise operation", type: .info)
        ExerciseLogger.shared.logExerciseData(exercise, operation: "UPDATE")
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)/\(exercise.id.uuidString)"
            ExerciseLogger.shared.log("Updating exercise at URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "PUT"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add authorization header
            guard let authHeaders = UserManager.shared.getAuthorizationHeader() else {
                ExerciseLogger.shared.log("❌ ERROR: No authorization header available for updateExercise", type: .error)
                throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                    NSLocalizedDescriptionKey: "Authorization header is missing. Please log in again."
                ])
            }
            for (key, value) in authHeaders {
                request.setValue(value, forHTTPHeaderField: key)
                ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
            }
            
            // Encode exercise data
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let jsonData = try encoder.encode(exercise)
            request.httpBody = jsonData
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "PUT", 
                headers: UserManager.shared.getAuthorizationHeader(),
                body: jsonData
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("Exercise updated successfully", type: .info)
                
                // Update local exercise list
                await MainActor.run {
                    if let index = exercises.firstIndex(where: { $0.id == exercise.id }) {
                        exercises[index] = exercise
                    }
                    isLoading = false
                }
            } else {
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                    NSLocalizedDescriptionKey: "Update Error (\(httpResponse.statusCode)): \(responseString)"
                ])
            }
        } catch {
            ExerciseLogger.shared.log("updateExercise failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // *** NEW: Delete exercise - DELETE /api/exercise/:id ***
    func deleteExercise(_ exerciseId: UUID) async throws {
        ExerciseLogger.shared.log("Starting deleteExercise operation for ID: \(exerciseId.uuidString)", type: .info)
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)/\(exerciseId.uuidString)"
            ExerciseLogger.shared.log("Deleting exercise at URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "DELETE"
            
            // Add authorization header
            guard let authHeaders = UserManager.shared.getAuthorizationHeader() else {
                ExerciseLogger.shared.log("❌ ERROR: No authorization header available for deleteExercise", type: .error)
                throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                    NSLocalizedDescriptionKey: "Authorization header is missing. Please log in again."
                ])
            }
            for (key, value) in authHeaders {
                request.setValue(value, forHTTPHeaderField: key)
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "DELETE",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 || httpResponse.statusCode == 204 {
                ExerciseLogger.shared.log("Exercise deleted successfully", type: .info)
                
                // Remove from local exercise list
                await MainActor.run {
                    exercises.removeAll { $0.id == exerciseId }
                    isLoading = false
                }
            } else {
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                    NSLocalizedDescriptionKey: "Delete Error (\(httpResponse.statusCode)): \(responseString)"
                ])
            }
        } catch {
            ExerciseLogger.shared.log("deleteExercise failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // *** UPDATED: Get student exercises - GET /api/student/exercises ***
    // Now uses JWT authentication to automatically identify the user
    func getStudentExercises() async throws {
        ExerciseLogger.shared.log("Starting getStudentExercises using JWT authentication", type: .info)
        
        // Check authentication before making request
        do {
            try UserManager.shared.requireAuthentication()
        } catch {
            ExerciseLogger.shared.log("Authentication check failed: \(error.localizedDescription)", type: .error)
            throw error
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = APIConfig.studentExercisesEndpoint
            ExerciseLogger.shared.log("Fetching student exercises from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                // Log the raw response for debugging
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Raw response data: \(responseString)", type: .debug)
                }
                
                let decoder = JSONDecoder()
                
                do {
                    // Try to decode as array of exercises first
                    let studentExercises = try decoder.decode([Exercise].self, from: data)
                    ExerciseLogger.shared.log("Successfully decoded \(studentExercises.count) student exercises", type: .info)
                    
                    await MainActor.run {
                        self.exercises = studentExercises
                        isLoading = false
                    }
                } catch let decodingError {
                    // If direct array decoding fails, try to decode as a wrapper object
                    ExerciseLogger.shared.log("Direct array decoding failed: \(decodingError)", type: .debug)
                    
                    do {
                        // Try decoding as a wrapper object that might contain the exercises array
                        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                            ExerciseLogger.shared.log("Response is an object with keys: \(json.keys.joined(separator: ", "))", type: .debug)
                            
                            // Look for common wrapper patterns
                            if let exercisesArray = json["exercises"] as? [[String: Any]] {
                                let exercisesData = try JSONSerialization.data(withJSONObject: exercisesArray)
                                let studentExercises = try decoder.decode([Exercise].self, from: exercisesData)
                                ExerciseLogger.shared.log("Successfully decoded \(studentExercises.count) student exercises from 'exercises' key", type: .info)
                                
                                await MainActor.run {
                                    self.exercises = studentExercises
                                    isLoading = false
                                }
                                return
                            } else if let exercisesArray = json["data"] as? [[String: Any]] {
                                let exercisesData = try JSONSerialization.data(withJSONObject: exercisesArray)
                                let studentExercises = try decoder.decode([Exercise].self, from: exercisesData)
                                ExerciseLogger.shared.log("Successfully decoded \(studentExercises.count) student exercises from 'data' key", type: .info)
                                
                                await MainActor.run {
                                    self.exercises = studentExercises
                                    isLoading = false
                                }
                                return
                            } else if let exercisesArray = json["results"] as? [[String: Any]] {
                                let exercisesData = try JSONSerialization.data(withJSONObject: exercisesArray)
                                let studentExercises = try decoder.decode([Exercise].self, from: exercisesData)
                                ExerciseLogger.shared.log("Successfully decoded \(studentExercises.count) student exercises from 'results' key", type: .info)
                                
                                await MainActor.run {
                                    self.exercises = studentExercises
                                    isLoading = false
                                }
                                return
                            }
                        }
                        
                        // If we get here, throw the original decoding error
                        throw decodingError
                    } catch {
                        ExerciseLogger.shared.log("JSON parsing also failed: \(error)", type: .error)
                        throw decodingError
                    }
                }
            } else {
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                    NSLocalizedDescriptionKey: "Student Exercises Error (\(httpResponse.statusCode)): \(responseString)"
                ])
            }
        } catch {
            ExerciseLogger.shared.log("getStudentExercises failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // *** NEW: Teacher method to get exercises for any specific student ***
    func getStudentExercises(for studentId: String) async throws {
        ExerciseLogger.shared.log("Starting getStudentExercises for specific student ID: \(studentId) (Teacher access)", type: .info)
        
        // Check authentication before making request
        do {
            try UserManager.shared.requireAuthentication()
        } catch {
            ExerciseLogger.shared.log("Authentication check failed: \(error.localizedDescription)", type: .error)
            throw error
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.studentExercisesEndpoint)?studentId=\(studentId)"
            ExerciseLogger.shared.log("Fetching student exercises from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                let decoder = JSONDecoder()
                
                let studentExercises = try decoder.decode([Exercise].self, from: data)
                ExerciseLogger.shared.log("Successfully decoded \(studentExercises.count) student exercises for student \(studentId)", type: .info)
                
                await MainActor.run {
                    self.exercises = studentExercises
                    isLoading = false
                }
            } else if httpResponse.statusCode == 401 {
                let errorMessage = "Authentication failed. Please log in again."
                ExerciseLogger.shared.log("Authentication error (401): \(errorMessage)", type: .error)
                
                // Clear the user session and redirect to login
                await MainActor.run {
                    UserManager.shared.logout()
                }
                
                throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                    NSLocalizedDescriptionKey: errorMessage
                ])
            } else if httpResponse.statusCode == 403 {
                let errorMessage = "Access denied. You don't have permission to view this student's exercises."
                ExerciseLogger.shared.log("Access denied (403): \(errorMessage)", type: .error)
                
                throw NSError(domain: "AuthorizationError", code: 403, userInfo: [
                    NSLocalizedDescriptionKey: errorMessage
                ])
            } else {
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                    NSLocalizedDescriptionKey: "Student Exercises Error (\(httpResponse.statusCode)): \(responseString)"
                ])
            }
        } catch {
            ExerciseLogger.shared.log("getStudentExercises for student \(studentId) failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // Calculate score for a submission
    func calculateScore(for submission: StudentSubmission, exercise: Exercise) -> Double {
        var totalPoints = 0.0
        var earnedPoints = 0.0
        
        for question in exercise.questions {
            totalPoints += Double(question.points)
            if let answer = submission.answers.first(where: { $0.questionId == question.id }) {
                // Check if the answer is correct by comparing with correctAnswerIndex
                // This is more reliable than relying on the isCorrect field from backend
                if let answerIndex = Int(answer.answer),
                   answerIndex == question.correctAnswerIndex {
                    earnedPoints += Double(question.points)
                }
            }
        }
        
        return totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0
    }
    
    // Handle enhanced submission response
    func handleSubmissionResponse(_ response: [String: Any]) -> SubmissionResult {
        let score = response["score"] as? Double ?? 0.0
        let gradingStatus = response["gradingStatus"] as? String ?? ""
        let summary = response["summary"] as? [String: Any] ?? [:]
        
        let autoGraded = summary["autoGraded"] as? Bool ?? false
        let totalQuestions = summary["totalQuestions"] as? Int ?? 0
        let answeredQuestions = summary["answeredQuestions"] as? Int ?? 0
        let multipleChoiceCorrect = summary["multipleChoiceCorrect"] as? Int ?? 0
        let longAnswerCount = summary["longAnswerCount"] as? Int ?? 0
        
        return SubmissionResult(
            score: score,
            gradingStatus: gradingStatus,
            autoGraded: autoGraded,
            totalQuestions: totalQuestions,
            answeredQuestions: answeredQuestions,
            multipleChoiceCorrect: multipleChoiceCorrect,
            longAnswerCount: longAnswerCount
        )
    }

    // *** NEW: Get all submissions for the current student ***
    func getAllStudentSubmissions() async throws {
        // Skip if already loading to prevent multiple concurrent calls
        if isLoadingSubmissions {
            ExerciseLogger.shared.log("getAllStudentSubmissions already in progress, skipping duplicate call", type: .debug)
            return
        }
        
        // Check cache first
        if !shouldRefreshSubmissions {
            ExerciseLogger.shared.log("Using cached submissions (loaded \(Date().timeIntervalSince(lastSubmissionLoadTime ?? Date())) seconds ago)", type: .debug)
            return
        }
        
        ExerciseLogger.shared.log("Starting getAllStudentSubmissions operation for current student", type: .info)
        
        await MainActor.run {
            isLoadingSubmissions = true
            error = nil
        }
        
        do {
            let urlString = APIConfig.submissionEndpoint
            ExerciseLogger.shared.log("Fetching all student submissions from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            // Create request with authentication
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                    ExerciseLogger.shared.log("Added auth header: \(key)", type: .debug)
                }
            } else {
                ExerciseLogger.shared.log("⚠️ Warning: No authorization token available", type: .debug)
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString, 
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("Successfully received submission data", type: .info)
                
                let decoder = JSONDecoder()
                
                // Log the raw JSON for debugging before decoding
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Raw submissions response: \(responseString)", type: .debug)
                }
                
                let submissions = try decoder.decode([StudentSubmission].self, from: data)
                ExerciseLogger.shared.log("Successfully decoded \(submissions.count) total submissions", type: .info)
                
                // Filter for current student only
                let currentStudentId = UserManager.shared.currentUser.id
                let filteredSubmissions = submissions.filter { $0.studentId == currentStudentId }
                
                ExerciseLogger.shared.log("Filtered to \(filteredSubmissions.count) submissions for current student: \(currentStudentId)", type: .info)
                
                // Log each submission for debugging
                for (index, submission) in filteredSubmissions.enumerated() {
                    ExerciseLogger.shared.log("Submission \(index + 1): Exercise \(submission.exerciseId.uuidString)", type: .debug)
                    ExerciseLogger.shared.log("  - UUID: \(submission.id.uuidString)", type: .debug)
                    ExerciseLogger.shared.log("  - MongoDB ObjectId: \(submission.mongoId ?? "nil")", type: .debug)
                    ExerciseLogger.shared.log("  - Score: \(submission.score?.description ?? "nil")", type: .debug)
                    ExerciseLogger.shared.log("  - Answers: \(submission.answers.count)", type: .debug)
                }
                
                await MainActor.run {
                    self.studentSubmissions = filteredSubmissions
                    self.isLoadingSubmissions = false
                    self.lastSubmissionLoadTime = Date()
                }
                
                ExerciseLogger.shared.log("getAllStudentSubmissions completed successfully", type: .info)
                
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                }
                
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                let detailedMessage = "Server Error (\(httpResponse.statusCode)): \(responseString)"
                throw NSError(domain: "NetworkError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: detailedMessage])
            }
        } catch {
            ExerciseLogger.shared.log("getAllStudentSubmissions failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                self.isLoadingSubmissions = false
            }
            throw error
        }
    }

    // *** DEPRECATED: This method makes multiple API calls - use getAllStudentSubmissions() instead ***
    @available(*, deprecated, message: "This method makes multiple API calls. Use getAllStudentSubmissions() instead.")
    func loadSubmissionsForExercises() async throws {
        ExerciseLogger.shared.log("⚠️ DEPRECATED: loadSubmissionsForExercises called - this makes multiple API calls!", type: .debug)
        ExerciseLogger.shared.log("⚠️ Use getAllStudentSubmissions() instead for better performance", type: .debug)
        
        // Redirect to the optimized method
        try await getAllStudentSubmissions()
    }
    
    // Clear submission cache for a specific exercise (for forced refresh)
    func clearSubmissionCache(for exerciseId: UUID) {
        exerciseSubmissionsCache.removeValue(forKey: exerciseId)
        ExerciseLogger.shared.log("Cleared submission cache for exercise: \(exerciseId.uuidString)", type: .debug)
    }
    
    // Clear all submission caches
    func clearAllSubmissionCaches() {
        exerciseSubmissionsCache.removeAll()
        lastSubmissionLoadTime = nil
        ExerciseLogger.shared.log("Cleared all submission caches", type: .debug)
    }
    
    // *** NEW: Get a specific exercise by ID - GET /api/exercise/:id ***
    func getExercise(by id: UUID) async throws -> Exercise {
        ExerciseLogger.shared.log("Starting getExercise operation for ID: \(id.uuidString)", type: .info)
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.exerciseEndpoint)/\(id.uuidString)"
            ExerciseLogger.shared.log("Fetching exercise from URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "GET",
                headers: UserManager.shared.getAuthorizationHeader()
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                let decoder = JSONDecoder()
                
                let exercise = try decoder.decode(Exercise.self, from: data)
                ExerciseLogger.shared.log("Successfully decoded exercise: \(exercise.title) (ID: \(exercise.id.uuidString))", type: .info)
                
                await MainActor.run {
                    isLoading = false
                }
                
                return exercise
            } else {
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                    NSLocalizedDescriptionKey: "Get Exercise Error (\(httpResponse.statusCode)): \(responseString)"
                ])
            }
        } catch {
            ExerciseLogger.shared.log("getExercise failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
    
    // *** NEW: Grade submission with multiple questions in a single request ***
    func gradeSubmissionWithMultipleQuestions(submission: StudentSubmission, grades: [QuestionGrade], feedback: String, gradingMethod: String = "manual") async throws {
        ExerciseLogger.shared.log("=== TEACHER GRADING OPERATION STARTED (Multiple Questions) ===", type: .info)
        ExerciseLogger.shared.log("🎯 Teacher is updating student grade for multiple questions", type: .info)
        ExerciseLogger.shared.log("📝 Submission UUID: \(submission.id.uuidString)", type: .info)
        ExerciseLogger.shared.log("📝 Submission MongoDB ID: \(submission.mongoId ?? "Not available")", type: .info)
        ExerciseLogger.shared.log("📊 Number of questions to grade: \(grades.count)", type: .info)
        ExerciseLogger.shared.log("💬 Overall Feedback: \(feedback)", type: .info)
        ExerciseLogger.shared.log("🔧 Grading Method: \(gradingMethod)", type: .info)
        ExerciseLogger.shared.log("👨‍🏫 Teacher ID: \(UserManager.shared.currentUser.id)", type: .info)
        ExerciseLogger.shared.log("👨‍🏫 Teacher Name: \(UserManager.shared.currentUser.name)", type: .info)
        
        // Ensure we have the MongoDB ObjectId for the backend
        guard let mongoId = submission.mongoId else {
            ExerciseLogger.shared.log("❌ ERROR: No MongoDB ObjectId available for submission", type: .error)
            throw NSError(domain: "SubmissionError", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Cannot grade submission: MongoDB ObjectId is missing. Please refresh the submission data."
            ])
        }
        
        ExerciseLogger.shared.log("✅ Using MongoDB ObjectId for backend: \(mongoId)", type: .info)
        
        // Ensure valid authentication and get authorization header
        var authHeaders: [String: String]
        
        ExerciseLogger.shared.log("🔐 Starting authentication check for grading...", type: .info)
        ExerciseLogger.shared.log("👤 Current user: \(UserManager.shared.currentUser.name) (ID: \(UserManager.shared.currentUser.id))", type: .info)
        ExerciseLogger.shared.log("🔑 Is logged in: \(UserManager.shared.isLoggedIn)", type: .info)
        
        if let headers = UserManager.shared.getAuthorizationHeader() {
            authHeaders = headers
            ExerciseLogger.shared.log("✅ Authorization header available", type: .info)
            ExerciseLogger.shared.log("📋 Auth headers: \(headers)", type: .debug)
        } else {
            ExerciseLogger.shared.log("❌ ERROR: No authorization header available, attempting to refresh authentication", type: .error)
            
            // Try to ensure valid authentication first
            ExerciseLogger.shared.log("🔄 Attempting to ensure valid authentication...", type: .info)
            let authValid = await UserManager.shared.ensureValidAuthentication()
            ExerciseLogger.shared.log("🔍 Authentication validation result: \(authValid)", type: .info)
            
            if authValid {
                // Try again after ensuring authentication
                ExerciseLogger.shared.log("🔄 Trying to get authorization header again after validation...", type: .info)
                if let retryAuthHeaders = UserManager.shared.getAuthorizationHeader() {
                    authHeaders = retryAuthHeaders
                    ExerciseLogger.shared.log("✅ Authorization header available after authentication refresh", type: .info)
                    ExerciseLogger.shared.log("📋 Retry auth headers: \(retryAuthHeaders)", type: .debug)
                } else {
                    ExerciseLogger.shared.log("❌ Still no authorization header after authentication refresh", type: .error)
                    throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                        NSLocalizedDescriptionKey: "Authorization header is missing. Please log in again."
                    ])
                }
            } else {
                ExerciseLogger.shared.log("❌ Authentication validation failed", type: .error)
                throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                    NSLocalizedDescriptionKey: "Authentication failed. Please log in again."
                ])
            }
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let urlString = "\(APIConfig.submissionEndpoint)?submission_id=\(mongoId)"
            ExerciseLogger.shared.log("🌐 Constructing grading URL", type: .info)
            ExerciseLogger.shared.log("🔗 Base endpoint: \(APIConfig.submissionEndpoint)", type: .debug)
            ExerciseLogger.shared.log("🔗 Final URL: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL from: \(urlString)", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "PUT"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            ExerciseLogger.shared.log("📤 Preparing PUT request for grading", type: .info)
            ExerciseLogger.shared.log("🔐 Adding authentication headers", type: .debug)
            
            // Add authorization header (we already verified it exists above)
            for (key, value) in authHeaders {
                request.setValue(value, forHTTPHeaderField: key)
                ExerciseLogger.shared.log("   ✅ Added auth header: \(key)", type: .debug)
            }
            
            // Create grading data JSON with the new format
            ExerciseLogger.shared.log("📝 Creating grading payload with multiple questions", type: .info)
            
            // Convert grades to the format expected by the backend
            let gradesData = grades.map { grade in
                [
                    "questionId": grade.questionId.uuidString,
                    "pointsEarned": grade.pointsEarned,
                    "isCorrect": grade.isCorrect,
                    "feedback": grade.feedback
                ] as [String: Any]
            }
            let gradingData: [String: Any] = [
                "grades": gradesData,
                "gradingMethod": gradingMethod,
                "feedback": feedback
            ]
            
            ExerciseLogger.shared.log("📊 Grading data created:", type: .debug)
            ExerciseLogger.shared.log("   - Number of grades: \(grades.count)", type: .debug)
            ExerciseLogger.shared.log("   - Overall feedback: \(feedback)", type: .debug)
            ExerciseLogger.shared.log("   - Grading method: \(gradingMethod)", type: .debug)
            
            // Log individual grades for debugging
            for (index, grade) in grades.enumerated() {
                ExerciseLogger.shared.log("   Grade \(index + 1): Question \(grade.questionId.uuidString), Points: \(grade.pointsEarned), Correct: \(grade.isCorrect)", type: .debug)
            }
            
            let jsonData = try JSONSerialization.data(withJSONObject: gradingData)
            ExerciseLogger.shared.log("✅ Successfully encoded grading data (\(jsonData.count) bytes)", type: .info)
            
            // Log the JSON payload for debugging
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                ExerciseLogger.shared.log("📄 JSON payload being sent:", type: .debug)
                ExerciseLogger.shared.log(jsonString, type: .debug)
            }
            
            request.httpBody = jsonData
            ExerciseLogger.shared.log("📦 Request body attached to request", type: .debug)
            
            // Prepare headers for logging
            var requestHeaders = ["Content-Type": "application/json"]
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                requestHeaders.merge(authHeaders) { (_, new) in new }
            }
            
            ExerciseLogger.shared.logNetworkRequest(
                url: urlString,
                method: "PUT",
                headers: requestHeaders,
                body: jsonData
            )
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                ExerciseLogger.shared.log("Invalid response type - expected HTTPURLResponse", type: .error)
                throw NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            ExerciseLogger.shared.logNetworkResponse(
                statusCode: httpResponse.statusCode,
                headers: httpResponse.allHeaderFields as? [String: String],
                data: data
            )
            
            if httpResponse.statusCode == 200 {
                ExerciseLogger.shared.log("🎉 SUCCESS: Submission graded successfully with multiple questions!", type: .info)
                ExerciseLogger.shared.log("✅ HTTP Status: \(httpResponse.statusCode)", type: .info)
                ExerciseLogger.shared.log("📊 Grade updated for submission: \(mongoId)", type: .info)
                ExerciseLogger.shared.log("👨‍🏫 Graded by: \(UserManager.shared.currentUser.name) (\(UserManager.shared.currentUser.id))", type: .info)
                ExerciseLogger.shared.log("📈 Number of questions graded: \(grades.count)", type: .info)
                
                await MainActor.run {
                    isLoading = false
                }
                ExerciseLogger.shared.log("=== TEACHER GRADING OPERATION COMPLETED SUCCESSFULLY ===", type: .info)
            } else {
                ExerciseLogger.shared.log("Server returned error status code: \(httpResponse.statusCode)", type: .error)
                
                // Handle error response from backend server
                let responseString = String(data: data, encoding: .utf8) ?? "No response body"
                ExerciseLogger.shared.log("Error response body: \(responseString)", type: .error)
                
                // Try to parse structured error message
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorData["error"] as? String {
                    ExerciseLogger.shared.log("Structured server error message: \(errorMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: "Grading Error (\(httpResponse.statusCode)): \(errorMessage)",
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                } else {
                    // Provide detailed error with status code and response
                    let detailedMessage = "Grading Error (\(httpResponse.statusCode)): \(responseString)"
                    ExerciseLogger.shared.log("Unstructured server error: \(detailedMessage)", type: .error)
                    throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [
                        NSLocalizedDescriptionKey: detailedMessage,
                        "httpStatusCode": httpResponse.statusCode,
                        "serverResponse": responseString
                    ])
                }
            }
        } catch {
            ExerciseLogger.shared.log("gradeSubmissionWithMultipleQuestions failed with error: \(error.localizedDescription)", type: .error)
            await MainActor.run {
                self.error = error
                isLoading = false
            }
            throw error
        }
    }
}

// Enhanced submission result structure
struct SubmissionResult {
    let score: Double
    let gradingStatus: String
    let autoGraded: Bool
    let totalQuestions: Int
    let answeredQuestions: Int
    let multipleChoiceCorrect: Int
    let longAnswerCount: Int
    
    var isAutoGraded: Bool {
        return autoGraded
    }
    
    var needsManualGrading: Bool {
        return !autoGraded && longAnswerCount > 0
    }
    
    var resultMessage: String {
        if autoGraded {
            return "Your submission has been automatically graded. Score: \(Int(score))%"
        } else {
            return "Your submission is pending manual grading for long answer questions."
        }
    }
    
    // MARK: - Test Functions for Debugging
    
    /// Test function to verify logging system is working
    func testLogging() {
        ExerciseLogger.shared.log("=== TESTING EXERCISE LOGGER ===", type: .info)
        
        // Test basic logging
        ExerciseLogger.shared.log("Basic log test", type: .info)
        ExerciseLogger.shared.log("Debug log test", type: .debug)
        ExerciseLogger.shared.log("Error log test", type: .error)
        
        // Test date formats
        ExerciseLogger.shared.log("=== DATE FORMAT TESTING ===", type: .info)
        let testDate = Date()
        
        // ISO8601 format
        let iso8601Formatter = ISO8601DateFormatter()
        let iso8601String = iso8601Formatter.string(from: testDate)
        ExerciseLogger.shared.log("ISO8601 format: \(iso8601String)", type: .debug)
        
        // Custom UTC format
        let utcFormatter = DateFormatter()
        utcFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        utcFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let utcString = utcFormatter.string(from: testDate)
        ExerciseLogger.shared.log("UTC format: \(utcString)", type: .debug)
        
        // Simple date format
        let simpleDateFormatter = DateFormatter()
        simpleDateFormatter.dateFormat = "yyyy-MM-dd"
        let simpleDateString = simpleDateFormatter.string(from: testDate)
        ExerciseLogger.shared.log("Simple date format: \(simpleDateString)", type: .debug)
        
        // RFC3339 format
        let rfc3339Formatter = DateFormatter()
        rfc3339Formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXXXX"
        rfc3339Formatter.timeZone = TimeZone(abbreviation: "UTC")
        let rfc3339String = rfc3339Formatter.string(from: testDate)
        ExerciseLogger.shared.log("RFC3339 format: \(rfc3339String)", type: .debug)
        
        ExerciseLogger.shared.log("=== END DATE FORMAT TESTING ===", type: .info)
        ExerciseLogger.shared.log("=== END TESTING ===", type: .info)
    }
    
    /// Test function to verify API connectivity
    func testAPIConnectivity() async -> Bool {
        ExerciseLogger.shared.log("=== API CONNECTIVITY TEST ===", type: .info)
        
        do {
            let urlString = APIConfig.baseURL
            ExerciseLogger.shared.log("Testing connectivity to: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL", type: .error)
                return false
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD"
            request.timeoutInterval = 10
            
            // Add auth headers if available
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                ExerciseLogger.shared.log("Server responded with status: \(httpResponse.statusCode)", type: .info)
                ExerciseLogger.shared.log("=== API CONNECTIVITY SUCCESS ===", type: .info)
                return httpResponse.statusCode < 400
            } else {
                ExerciseLogger.shared.log("Invalid response type", type: .error)
                return false
            }
        } catch {
            ExerciseLogger.shared.log("API connectivity test failed: \(error.localizedDescription)", type: .error)
            ExerciseLogger.shared.log("=== API CONNECTIVITY FAILED ===", type: .error)
            return false
        }
    }
    
    /// Quick ping test to check if the server is reachable
    func pingServer() async -> Bool {
        ExerciseLogger.shared.log("=== PING SERVER TEST ===", type: .info)
        
        do {
            let urlString = APIConfig.exerciseEndpoint
            ExerciseLogger.shared.log("Pinging server at: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("❌ Invalid URL for ping test", type: .error)
                return false
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD" // HEAD request is faster than GET
            request.timeoutInterval = 10.0 // 10 second timeout
            
            let startTime = Date()
            let (_, response) = try await URLSession.shared.data(for: request)
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            if let httpResponse = response as? HTTPURLResponse {
                ExerciseLogger.shared.log("✅ Server responded with status \(httpResponse.statusCode) in \(String(format: "%.2f", duration)) seconds", type: .info)
                return httpResponse.statusCode < 500 // Any non-server error status is considered "reachable"
            } else {
                ExerciseLogger.shared.log("❌ Invalid response type from server", type: .error)
                return false
            }
        } catch {
            ExerciseLogger.shared.log("❌ Server ping failed: \(error.localizedDescription)", type: .error)
            return false
        }
    }
    
    /// Test function to check server response format with manual JSON
    func testManualJSONCreation() async -> Bool {
        ExerciseLogger.shared.log("=== MANUAL JSON CREATION TEST ===", type: .info)
        
        do {
            let urlString = APIConfig.exerciseEndpoint
            ExerciseLogger.shared.log("Testing manual JSON creation at: \(urlString)", type: .info)
            
            guard let url = URL(string: urlString) else {
                ExerciseLogger.shared.log("Failed to create URL", type: .error)
                return false
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add auth headers if available
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
            
            // Create minimal JSON manually
            let manualJSON: [String: Any] = [
                "id": UUID().uuidString,
                "title": "Manual JSON Test",
                "topic": "Testing",
                "subtopic": "Manual Creation",
                "classroomId": "TEST",
                "questions": [],
                "createdBy": UserManager.shared.currentUser.id,
                "createdAt": ISO8601DateFormatter().string(from: Date()),
                "dueDate": ISO8601DateFormatter().string(from: Date().addingTimeInterval(24 * 60 * 60))
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: manualJSON)
            
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                ExerciseLogger.shared.log("Manual JSON being sent: \(jsonString)", type: .debug)
            }
            
            request.httpBody = jsonData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                ExerciseLogger.shared.log("Manual JSON test response status: \(httpResponse.statusCode)", type: .info)
                
                if let responseString = String(data: data, encoding: .utf8) {
                    ExerciseLogger.shared.log("Manual JSON test response: \(responseString)", type: .debug)
                }
                
                ExerciseLogger.shared.log("=== MANUAL JSON TEST SUCCESS ===", type: .info)
                return httpResponse.statusCode == 201
            } else {
                ExerciseLogger.shared.log("Invalid response type", type: .error)
                return false
            }
        } catch {
            ExerciseLogger.shared.log("Manual JSON test failed: \(error.localizedDescription)", type: .error)
            ExerciseLogger.shared.log("=== MANUAL JSON TEST FAILED ===", type: .error)
            return false
        }
    }
} 
